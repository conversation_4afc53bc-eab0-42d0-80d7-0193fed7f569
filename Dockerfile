ARG NODE_VERSION=20.11.1
FROM --platform=linux/x86_64 817265962114.dkr.ecr.us-east-1.amazonaws.com/devops-common-images:node_20.11.1-alpine

LABEL ORG="Netomi"
LABEL AUTHOR_NAME="<PERSON>asu <PERSON>"
LABEL AUTHOR_EMAIL="<EMAIL>"

RUN set -uex; \
    apk update; \
    apk add py-pip; \
    pip install supervisor supervisor-stdout  --break-system-packages;

RUN npm i --global typescript ts-node

# Create directory for app
RUN mkdir -p /app

# Create logs directory
RUN mkdir -p /var/log/app-logs

COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY start.sh /bin/start.sh
RUN chmod 755 /bin/start.sh

# Copy project files to container
COPY app/ /app/
WORKDIR /app
RUN npm install
RUN npm run build

EXPOSE 4000

CMD npm run setup-env && npm run cp-env && /bin/start.sh
