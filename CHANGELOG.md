# Revisions

## release/v2025.3.0.0

- 0.0.1 - Initial commit - Basic features, db, types, setup-env, run
- 0.0.2 - putFlag, putFlagRole APIs
- 0.0.3 - postFlagProfile (Search API) by multiple params
- 0.0.4 - Flag subscriptions via WebSockets
- 0.0.5 - Flag Variant APIs
- 0.0.6 - Audit Flags
- 0.0.7 - Filter getFlags and postFlagProfiles by active param
- 0.0.8 - Removes flag-role limit; log stds
- 0.0.9 - Accept both active and flagActive props in payload for post-flags api
- 0.1.0 - Accept both onValue and onVariant prpps in payload for put-flag-role api
