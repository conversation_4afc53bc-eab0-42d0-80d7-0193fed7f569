# Feature Flag Service

This service manages feature flags for Netomi Software Products.

## Build

```sh
git clone https://github.com/msgai/feature-flag-service
cd feature-flag-service/app
npm i
```

## Setup Env

```sh
cd feature-flag-service/app
# Ensures ./dist is created
npm run build
# Get the env from config
. ./.env.local
npm run setup-env
npm run cp-env
```

## Run

```sh
cd feature-flag-service/app
# First run only or whenever config changes
npm run dev
```
