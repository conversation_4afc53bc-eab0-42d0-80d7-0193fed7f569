AWS_ECR:=817265962114.dkr.ecr.us-east-1.amazonaws.com
APP_ENV:=local
IMAGE_NAME:=feature-flag-service
IMAGE_TAG:=${APP_ENV}
CONFIG_REGION=us-east-1
CONFIG_SERVICE_NAME=feature-flag-service
CONFIG_HOST=http://configmanager-dev1.internal.netomi.com/v1/service/configuration/get
CONFIG_ENV=dev1
PORT=4001

all: compile build-docker
compile:
	echo Nothing to compile.

# Target used by <PERSON>
build: compile
	docker build \
		--platform=linux/x86_64 \
		-t ${AWS_ECR}/${IMAGE_NAME}:${IMAGE_TAG} .

build-docker:
	docker build \
		--platform=linux/x86_64 \
		-t ${IMAGE_NAME}:${IMAGE_TAG} .

sh-docker:
	docker run -it \
		--platform=linux/x86_64 \
		-e APP_ENV=${APP_ENV} \
		-e CONFIG_REGION=${CONFIG_REGION} \
		-e CONFIG_HOST=${CONFIG_HOST} \
		-e CONFIG_ENV=${CONFIG_ENV} \
		-e CONFIG_SERVICE_NAME=${CONFIG_SERVICE_NAME} \
		-v ${HOME}/.aws:/root/.aws \
		-p ${PORT}:${PORT} \
		--entrypoint /bin/sh \
		${IMAGE_NAME}:${IMAGE_TAG}

run-docker:
	docker run -d \
		--platform=linux/x86_64 \
		-e APP_ENV=${APP_ENV} \
		-e CONFIG_REGION=${CONFIG_REGION} \
		-e CONFIG_HOST=${CONFIG_HOST} \
		-e CONFIG_ENV=${CONFIG_ENV} \
		-e CONFIG_SERVICE_NAME=${CONFIG_SERVICE_NAME} \
		-v ${HOME}/.aws:/root/.aws \
		-p ${PORT}:${PORT} \
		${IMAGE_NAME}:${IMAGE_TAG}

push:
	docker push ${AWS_ECR}/${IMAGE_NAME}:${IMAGE_TAG}