const axios = require('axios');

// Test script to verify the delete flags API
async function testDeleteFlags() {
    const baseURL = 'http://localhost:4010'; // Adjust if your server runs on a different port
    
    try {
        // Test data
        const testPayload = {
            flagNames: ['test-flag-1', 'test-flag-2'] // Replace with actual flag names in your database
        };

        console.log('Testing delete flags API...');
        console.log('Payload:', JSON.stringify(testPayload, null, 2));

        const response = await axios.post(`${baseURL}/api/flags/delete`, testPayload, {
            headers: {
                'Content-Type': 'application/json',
                // Add any required authentication headers here
                // 'Authorization': 'Bearer your-token-here'
            }
        });

        console.log('Success Response:');
        console.log('Status:', response.status);
        console.log('Data:', JSON.stringify(response.data, null, 2));

    } catch (error) {
        console.error('Error Response:');
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Data:', JSON.stringify(error.response.data, null, 2));
        } else {
            console.log('Error:', error.message);
        }
    }
}

// Test with empty array (should fail)
async function testDeleteFlagsEmpty() {
    const baseURL = 'http://localhost:4010';
    
    try {
        const testPayload = {
            flagNames: []
        };

        console.log('\nTesting delete flags API with empty array...');
        console.log('Payload:', JSON.stringify(testPayload, null, 2));

        const response = await axios.post(`${baseURL}/api/flags/delete`, testPayload, {
            headers: {
                'Content-Type': 'application/json'
            }
        });

        console.log('Unexpected Success Response:');
        console.log('Status:', response.status);
        console.log('Data:', JSON.stringify(response.data, null, 2));

    } catch (error) {
        console.log('Expected Error Response:');
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Data:', JSON.stringify(error.response.data, null, 2));
        } else {
            console.log('Error:', error.message);
        }
    }
}

// Test with non-existent flags (should fail)
async function testDeleteFlagsNotFound() {
    const baseURL = 'http://localhost:4010';
    
    try {
        const testPayload = {
            flagNames: ['non-existent-flag-1', 'non-existent-flag-2']
        };

        console.log('\nTesting delete flags API with non-existent flags...');
        console.log('Payload:', JSON.stringify(testPayload, null, 2));

        const response = await axios.post(`${baseURL}/api/flags/delete`, testPayload, {
            headers: {
                'Content-Type': 'application/json'
            }
        });

        console.log('Unexpected Success Response:');
        console.log('Status:', response.status);
        console.log('Data:', JSON.stringify(response.data, null, 2));

    } catch (error) {
        console.log('Expected Error Response:');
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Data:', JSON.stringify(error.response.data, null, 2));
        } else {
            console.log('Error:', error.message);
        }
    }
}

// Run all tests
async function runTests() {
    await testDeleteFlags();
    await testDeleteFlagsEmpty();
    await testDeleteFlagsNotFound();
}

runTests();
