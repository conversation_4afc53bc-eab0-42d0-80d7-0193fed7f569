import { INestApplication } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { readJSONFile } from '../utils/files';

export const serveSwaggerDocumentation = (app: INestApplication) => {
	const config = new DocumentBuilder()
		.setTitle('Netomi Feature Flag Service')
		.setDescription('Manage Feature Flags for Netomi Services')
		.setVersion(readJSONFile('.', 'package.json')?.version)
		.addBearerAuth(
			{
				description: 'Enter token in this format: Bearer <access_token>',
				name: 'authorization',
				in: 'header',
				type: 'apiKey',
				bearerFormat: 'bearer',
			},
			'authorization',
		)
		.addSecurityRequirements('authorization')
		.build();
	const document = SwaggerModule.createDocument(app, config);
	SwaggerModule.setup('api/docs', app, document);
};
