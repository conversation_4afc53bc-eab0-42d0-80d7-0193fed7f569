import path from 'path';

export const appEnv = process.env.APP_ENV;
export const rootFolder = path.join(__dirname, '../../');
export const storageFolder = `${appEnv}/feature-flag`; //Without trailing slash
export const isProd = appEnv === 'production' || appEnv === 'prod';

export const AppErrors = {
	AUTH_ACCESS_DENIED: 'Access Denied',

	USER_NOT_FOUND: 'Failed to find User',
	USER_INACTIVE: 'User is inactive',
	USER_DOES_NOT_BELONG_TO_ORG: 'User does not belong to this Org',
	USER_NOT_APPROVED_FOR_THIS_ORG: "User email is not in the Org's approved domain list",

	FLAG_NOT_FOUND: 'Failed to find Feature Flag',

	ORG_NOT_FOUND: 'Failed to find Org',
	ORG_INACTIVE: 'Org is inactive',

	ROLE_NOT_FOUND: 'Failed to find Role',
	ROLE_DEFAULT_NOT_FOUND: 'Failed to find default Role to set role for AI Agent Based user',
	ROLE_INACTIVE: 'Role is inactive',
	ROLE_DENIED: 'Permission denied to apply this role to the user',

	POLICY_NOT_FOUND: 'Failed to find Policy',
	POLICY_INACTIVE: 'Policy is inactive',

	BOT_NOT_FOUND: 'Failed to find Bot',
	BOT_INACTIVE: 'Bot is inactive',
	BOT_ID_REQUIRED: 'AI Agent Id is required',

	INVALID_ENTITLEMENTS_ENTITY: 'Entitlements entity is not available',
} as const;
