import { AppDataSource } from '../db/app-data-source';
import * as log from '../utils/log';

export const initServices = async () => {
	log.enter('initServices');
	try {
		await initializeDb();
		log.info('initServices: successfully initialized DB services');
	} catch (e) {
		log.error('initServices: failed to initialize DB services');
		throw e;
	}

	try {
		// const ssoConfigs = (await loadSsoConfigs()) as SsoConfig[];
		// const ce = ConfigEnv.getInstance();
		// ce.ssoConfigs = ssoConfigs;
		// log.info('InitService: successfully initialized SSO module');
		// ce.showVars();
	} catch (e) {
		log.error('InitService: failed to initialize SSO module');
		throw e;
	}
	log.exit('initServices');
};

const initializeDb = async () => {
	await AppDataSource.getInstance().initialize();
};
