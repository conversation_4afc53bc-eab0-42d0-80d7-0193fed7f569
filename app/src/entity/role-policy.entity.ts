import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, CreateDateColumn, UpdateDateColumn, JoinColumn, ManyToOne } from 'typeorm';
import { Policy } from './policy.entity';
import { Role } from './role.entity';

@Entity('role_policies')
export class RolePolicy {
	@PrimaryGeneratedColumn({
		unsigned: true,
	})
	id: number;

	@Column({
		name: 'role_id',
	})
	roleId: number;

	@Column({
		name: 'policy_id',
	})
	policyId: number;

	@Column({
		name: 'active',
	})
	active: number;

	@Column({
		name: 'created_by',
	})
	createdBy: number;

	@Column({
		name: 'updated_by',
	})
	updatedBy: number;

	@CreateDateColumn({
		name: 'created_at',
		type: 'timestamp',
	})
	createdAt: Date;

	@UpdateDateColumn({
		name: 'updated_at',
		type: 'timestamp',
	})
	updatedAt: Date;

	@ManyToOne(() => Role, (role) => role.id)
	@JoinColumn({ name: 'roleId' })
	role: Role;

	@ManyToOne(() => Policy, (policy) => policy.id)
	@JoinColumn({ name: 'policyId' })
	policy: Policy;
}
