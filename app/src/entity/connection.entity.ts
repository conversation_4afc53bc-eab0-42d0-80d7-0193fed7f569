import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('ff_connections')
export class Connection {
	@PrimaryGeneratedColumn()
	id: number;

	@Column({ name: 'client_id' })
	clientId: string;

	@Column({ name: 'client_name' })
	clientName: string;

	@Column({ name: 'client_ip' })
	clientIp: string;

	@Column({ name: 'client_host' })
	clientHost: string;

	@Column({ name: 'flags' })
	flags: string;

	@Column({ name: 'status' })
	status: 'connect' | 'disconnect' | 'polling' | 'unreachable';

	@Column()
	callbackType: 'Rest' | 'WebSocket' | 'Webhook';

	@Column('simple-json')
	settings: string;

	@Column('simple-json')
	notes: string;

	@Column({ name: 'active' })
	active: number;

	@CreateDateColumn({
		name: 'issued_at',
		type: 'timestamp',
	})
	issuedAt: Date;

	@CreateDateColumn({
		name: 'created_at',
		type: 'timestamp',
	})
	createdAt: Date;

	@CreateDateColumn({
		name: 'updated_at',
		type: 'timestamp',
	})
	updatedAt: Date;

	@UpdateDateColumn({
		name: 'disconnected_at',
		type: 'timestamp',
	})
	disconnectedAt: Date;
}
