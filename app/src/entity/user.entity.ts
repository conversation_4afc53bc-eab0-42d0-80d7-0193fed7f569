import { AfterLoad, Column, CreateDateColumn, Entity, JoinColumn, JoinTable, ManyToMany, ManyToOne, OneToMany, OneToOne, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

import { Role } from './role.entity';
import { Org } from './org.entity';
import { UserRole } from './user-role.entity';
import { StudioUser } from './studio-user.entity';
import { ROLES } from '../utils/constants';
import { FlagRole } from './flag-role.entity';

@Entity('users')
export class User {
	@PrimaryGeneratedColumn({
		unsigned: true,
	})
	id: number;

	@Column({
		name: 'org_id',
		unsigned: true,
	})
	orgId: number;

	@Column({
		name: 'first_name',
	})
	firstName: string;

	@Column({
		name: 'last_name',
	})
	lastName: string;

	@Column()
	email: string;

	@Column()
	uuid: string;

	@Column('simple-json')
	settings: {};

	@Column('simple-json')
	policy: string;

	@Column()
	role: string;

	@Column({
		name: 'manager_user_id',
		nullable: true,
	})
	managerUserId?: number;

	@Column()
	active: number;

	@Column({
		name: 'created_by',
	})
	createdBy: number;

	@Column({
		name: 'updated_by',
	})
	updatedBy: number;

	@CreateDateColumn({
		name: 'created_at',
		type: 'timestamp',
	})
	createdAt: Date;

	@UpdateDateColumn({
		name: 'updated_at',
		type: 'timestamp',
	})
	updatedAt: Date;

	@ManyToOne((_type) => Org, (org) => org.users)
	@JoinColumn({ name: 'org_id' })
	org: Org;

	@ManyToMany(() => Role) // Assuming you want to eagerly load roles
	@JoinTable({
		name: 'user_roles',
		joinColumn: { name: 'user_id' },
		inverseJoinColumn: { name: 'role_id' },
	})
	roles: Role[];

	@OneToMany(() => UserRole, (ur) => ur.user)
	userRoles: UserRole[];

	@OneToOne(() => StudioUser, (su) => su.passport)
	@JoinColumn({ name: 'uuid', referencedColumnName: 'id' })
	studioUser: StudioUser;

	@OneToMany(() => FlagRole, (flagRole) => flagRole.user)
	flagRoles: FlagRole[];

	/**
	 * Get all permitted orgs for this user, based on userRoles.orgId
	 * For SuperAdmin, userRoles.orgId should be null
	 * @returns Array of orgIds or a null( Superadmin )
	 */
	@AfterLoad()
	getPermittedOrgs(): Array<number> {
		// find all orgId-s currently this user has access to in user_roles table
		// User is populated either via findAuthor or findUserProfile
		if (this.roles) {
			// findAuthor groups by roles
			const inOrgs = this.roles
				?.map((x: { userRoles: any }) => x.userRoles)
				.flat()
				.filter((x) => x.active === 1)
				.map((x: { orgId: any }) => x.orgId);
			// eliminate NULLs which would indicate SuperAdmin
			this.permittedOrgs = inOrgs?.filter((x) => x !== null && x !== undefined) || [];
			// eliminate duplicates
		} else if (this.userRoles) {
			// findUserProfile groups by userRoles
			this.permittedOrgs =
				this.userRoles
					.filter((x) => x.active === 1 || (x.roleId === ROLES.AIAgentObserver && x.active === 0))
					.map((x: { orgId: any }) => x.orgId)
					.filter((y) => y !== null && y !== undefined) || [];
		} else {
			// if no roles or userRoles found, return the users orgId itself, to avoid confusion with [] which implies all orgs permitted
			this.permittedOrgs = [-1];
		}
		// Retain only distinct set of orgs
		this.permittedOrgs = Array.from(new Set(this.permittedOrgs));
		return this.permittedOrgs;
	}

	/** Transient Attributes */

	// Photo Url from IDP
	photoUrl: string;

	// Default Bot Id associated for this user, when user logs in but has no cookies set
	defaultBotId: string;

	// transit traceId, to trace a given request throughout the call */
	traceId: string;

	// Permitted Orgs for this user, calculated by AfterLoad()
	permittedOrgs: Array<number>;

	redirectUri: string;

	/** Convenience functions */

	getDomain(): string {
		return this.email.split('@')[1];
	}

	isNetomiUser(): boolean {
		return this.email?.indexOf('@netomi.com') > 0;
	}

	isSuperAdmin(): boolean {
		return this.roles && this.roles.length === 1 && this.roles[0].id === 1;
	}

	isOrgAdmin(): boolean {
		let atleastOneOrgAdmin: Role[] | UserRole[] = [];
		if (this.roles) {
			atleastOneOrgAdmin = this.roles.filter((x) => x.tags.indexOf('OrgAdmin') > 0);
		} else if (this.userRoles) {
			atleastOneOrgAdmin = this.userRoles.filter((x) => x.roleId === 2);
		}
		return atleastOneOrgAdmin.length > 0;
	}

	hasAccessToOrg(otherOrgId: number): boolean {
		return this.permittedOrgs.length === 0 || this.permittedOrgs.indexOf(otherOrgId) >= 0;
	}

	/**
	 * getOrgsOfOrgAdmin()
	 * @returns all orgs for which this user is OrgAdmin
	 */
	getOrgsOfOrgAdmin(): Array<number> {
		let orgAdminOrgs: any[] = [];
		if (this.roles) {
			orgAdminOrgs = this.roles
				.filter((x) => x.id === 2)
				.map((x: { userRoles: any }) => x.userRoles)
				.flat()
				.filter((x) => x.active === 1)
				.map((x: { orgId: any }) => x.orgId);
		} else if (this.userRoles) {
			this.userRoles
				.filter((x) => x.active === 1)
				.filter((x) => x.roleId === 2)
				.map((x: { orgId: any }) => x.orgId)
				.filter((y) => y !== null && y !== undefined);
		}

		return Array.from(new Set(orgAdminOrgs));
	}

	isOrgAdminOf(otherOrgId: number): boolean {
		const orgAdminOrgs = this.getOrgsOfOrgAdmin();
		return orgAdminOrgs.indexOf(otherOrgId) >= 0;
	}

	getCommonOrgs(managedUserOrgs: Array<number> | number): Array<number> {
		const otherOrgs: Array<number> = Array.isArray(managedUserOrgs) ? managedUserOrgs : [managedUserOrgs];
		if (!this.permittedOrgs) return otherOrgs;
		// Author is SuperAdmin - so author has access to all orgs
		if (this.permittedOrgs?.length === 0) return otherOrgs;
		// Managed User is SuperAdmin - so author has access only his/her own orgs
		if (otherOrgs.length === 0) return this.permittedOrgs;
		// Author is not SuperAdmin - so author has access only to those orgs thats common for both
		return this.permittedOrgs.filter((x) => otherOrgs.includes(x));
	}

	getPermittedBots(): Array<string> {
		let bots: any[] = [];
		if (this.roles) {
			bots = this.roles
				.filter((x) => x.id >= 3 && x.id <= 7)
				.map((x: { userRoles: any }) => x.userRoles)
				.flat()
				.filter((x) => x.active === 1)
				.map((y) => y.botId);
		} else if (this.userRoles) {
			bots = this.userRoles
				.filter((x) => x.active === 1)
				.map((y) => y.botId)
				.filter((z) => z !== null && z !== undefined);
		}
		return Array.from(new Set(bots));
	}
}
