import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, JoinColumn, OneToOne, ManyToOne } from 'typeorm';
import { BotSocialConfig } from './bot-social-config.entity';
import { Org } from './org.entity';
import { OrgDivision } from './org-division.entity';
import { UserRole } from './user-role.entity';
import { BotRole } from './bot-role.entity';
import { FlagRole } from './flag-role.entity';

@Entity('studio.bots')
export class Bot {
	@PrimaryGeneratedColumn()
	id: number;

	@Column({ name: 'uuid' })
	botId: string;

	@Column({ name: 'account_name' })
	name: string;

	@Column({ name: 'description' })
	description: string;

	@Column({ name: 'channel_id' })
	channelId: string;

	@Column({ name: 'domain_id' })
	domainId: string;

	@Column({ name: 'support_desk_id' })
	supportDeskId: string;

	@Column({ name: 'commerce_platform_id' })
	commercePlatformId: string;

	@Column({ name: 'image_url' })
	imageUrl: string;

	@Column({ name: 'status' })
	status: string;

	@Column('simple-json', { name: 'admin_emails' })
	adminEmails: string;

	@Column({ name: 'is_deleted' })
	isDeleted: number;

	@Column({ name: 'is_knowledge_base_enabled' })
	isKbEnabled: number;

	@Column({ name: 'is_answer_ai_enabled' })
	isAnswerAiEnabled: number;

	@Column({ name: 'is_archived' })
	isArchived: number;

	@Column({ name: 'org_id' })
	orgId: number;

	@Column({ name: 'org_division_id' })
	orgDivisionId: number;

	@Column({ name: 'bot_phase' })
	phase: string;

	@Column({ name: 'language_type' })
	languageType: string;

	@Column({ name: 'alias' })
	alias: string;

	@Column({ name: 'organization' })
	organization: string;

	@Column({ name: 'industry' })
	industry: string;

	@Column({ name: 'ai_description' })
	aiDescription: string;

	@Column({ name: 'audience' })
	audience: string;

	@Column({ name: 'created_by' })
	createdBy: string;

	@Column({ name: 'updated_by' })
	updatedBy: string;

	@CreateDateColumn({ name: 'created_at', type: 'timestamp' })
	createdAt: Date;

	@UpdateDateColumn({ name: 'updated_at', type: 'timestamp' })
	updatedAt: Date;

	// One Bot can have Many BotRefs
	// studio.bots -> 1:1 -> studio.bot_social_config
	@OneToMany(() => BotSocialConfig, (bsc) => bsc.bot)
	@JoinColumn({ name: 'uuid', referencedColumnName: 'botId' })
	botRefs: BotSocialConfig[];

	// One Bot has one Bot Role for enabling Entitlements
	// studio.bots -> 1:1 -> passport.bot_roles
	@OneToOne(() => BotRole, (botRole) => botRole.roles)
	@JoinColumn({ name: 'uuid', referencedColumnName: 'botId' })
	botRole: BotRole;

	// One Bot can belong to One Org
	// studio.bots -> 1:1 -> passport.orgs
	@ManyToOne(() => Org, (org) => org.id)
	@JoinColumn({ name: 'org_id', referencedColumnName: 'id' })
	org: Org;

	// One Bot can belong to One OrgDivision
	// studio.bots -> 1:1 -> passport.org_divisions
	@OneToOne(() => OrgDivision, (orgDivision) => orgDivision.id)
	@JoinColumn({ name: 'org_division_id', referencedColumnName: 'id' })
	orgDivision: OrgDivision;

	// One bot can have many AI Agent Based Roles
	// studio.bots -> 1:M -> passport.user_roles
	@OneToMany(() => UserRole, (ur) => ur.bot)
	@JoinColumn({ name: 'uuid', referencedColumnName: 'uuid' })
	userRoles: UserRole[];

	// One bot can have many AI Agent Based Roles
	// studio.bots -> 1:M -> passport.user_roles
	@OneToMany(() => UserRole, (ur) => ur.org)
	@JoinColumn({ name: 'org_id', referencedColumnName: 'orgId' })
	userRoleOrgs: UserRole[];

	@OneToMany(() => FlagRole, (flagRole) => flagRole.bot)
	flagRoles: FlagRole[];

	accessedOn: number;
}
