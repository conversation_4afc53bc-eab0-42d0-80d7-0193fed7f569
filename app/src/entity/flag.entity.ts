import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, ManyToOne, JoinColumn } from 'typeorm';
import { FlagRole } from './flag-role.entity';
import { FlagVariant } from './flag-variant.entity';

@Entity('feature_flags')
export class FeatureFlag {
	@PrimaryGeneratedColumn({
		unsigned: true,
	})
	id: number;

	@Column({ name: 'name' })
	name: string;

	@Column({ name: 'description' })
	description: string;

	@Column({ name: 'type' })
	type: string;

	@Column({ name: 'stage' })
	stage: string;

	@Column({ name: 'status' })
	status: string;

	@Column({ name: 'version' })
	version: string;

	@Column({ name: 'tags' })
	tags: string;

	@Column('simple-json')
	settings: {};

	@Column({ name: 'default_variant' })
	defaultVariant: number;

	@Column({ name: 'off_variant' })
	offVariant: number;

	@Column({ name: 'comments' })
	comments: string;

	@Column({ name: 'active' })
	active: number;

	@Column({ name: 'created_by' })
	createdBy: number;

	@Column({ name: 'updated_by' })
	updatedBy: number;

	@CreateDateColumn({
		name: 'created_at',
		type: 'timestamp',
	})
	createdAt: Date;

	@UpdateDateColumn({
		name: 'updated_at',
		type: 'timestamp',
	})
	updatedAt: Date;

	@ManyToOne(() => FlagVariant, (ffv) => ffv.defaultFlags, { nullable: true })
	@JoinColumn({ name: 'default_variant', referencedColumnName: 'varIndex' })
	defaultValue: FlagVariant;

	@ManyToOne(() => FlagVariant, (ffv) => ffv.offFlags, { nullable: true })
	@JoinColumn({ name: 'off_variant', referencedColumnName: 'varIndex' })
	offValue: FlagVariant;

	@OneToMany(() => FlagRole, (flagRole) => flagRole.flag)
	flagRoles: FlagRole[];
}
