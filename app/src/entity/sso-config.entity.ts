import { Column, CreateDateColumn, Entity, OneToOne, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { Org } from './org.entity';

@Entity('sso_configs')
export class SsoConfig {
	@PrimaryGeneratedColumn({
		unsigned: true,
	})
	id: number;

	@Column({
		name: 'provider_instance',
	})
	providerInstance: string;

	@Column({
		name: 'provider_name',
	})
	providerName: string;

	@Column({
		name: 'provider_type',
	})
	providerType: string;

	@Column()
	version: string;

	@Column()
	description: string;

	@Column()
	active: number;

	@Column('simple-json')
	config: {};

	@Column({
		name: 'created_by',
	})
	createdBy: string;

	@CreateDateColumn({
		name: 'created_at',
		type: 'timestamp',
	})
	createdAt: Date;

	@UpdateDateColumn({
		name: 'updated_at',
		type: 'timestamp',
	})
	updatedAt: Date;

	@OneToOne(() => Org, (org) => org.ssoConfig)
	org: Org;
}
