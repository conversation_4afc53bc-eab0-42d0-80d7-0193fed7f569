import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

@Entity('resources')
export class Resource {
	@PrimaryGeneratedColumn({
		unsigned: true,
	})
	id: number;

	@Column()
	product: string;

	@Column()
	pod: string;

	@Column()
	feature: string;

	@Column()
	action: string;

	@Column()
	description: string;

	@Column()
	path: string;

	@Column()
	route: string;

	@Column({
		name: 'ui_element',
	})
	element: string;

	@Column({
		name: 'ui_label',
	})
	label: string;

	@Column({
		name: 'ui_selector',
	})
	selector: string;
}
