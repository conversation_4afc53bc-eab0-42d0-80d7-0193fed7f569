import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToMany, JoinTable, OneToMany } from 'typeorm';
import { User } from './user.entity';
import { Policy } from './policy.entity';
import { UserRole } from './user-role.entity';

@Entity('roles')
export class Role {
	@PrimaryGeneratedColumn({
		unsigned: true,
	})
	id: number;

	@Column({
		name: 'name',
	})
	name: string;

	@Column({
		name: 'description',
	})
	description: string;

	@Column({
		name: 'tags',
	})
	tags: string;

	@Column({
		name: 'active',
	})
	active: number;

	@Column({
		name: 'created_by',
	})
	createdBy: number;

	@Column({
		name: 'updated_by',
	})
	updatedBy: number;

	@CreateDateColumn({
		name: 'created_at',
		type: 'timestamp',
	})
	createdAt: Date;

	@UpdateDateColumn({
		name: 'updated_at',
		type: 'timestamp',
	})
	updatedAt: Date;

	// passport.roles -> M:M -> passport.users
	@ManyToMany(() => User)
	@JoinTable({
		name: 'user_roles',
		joinColumn: { name: 'role_id' },
		inverseJoinColumn: { name: 'user_id' },
	})
	users: User[];

	// One Role has Many UserRoles
	// passport.roles -> 1:M -> passport.user_roles
	@OneToMany(() => UserRole, (userRoles) => userRoles.role)
	userRoles: UserRole[];

	// passport.roles -> M:M -> passport.policies
	@ManyToMany((_type) => Policy)
	@JoinTable({
		name: 'role_policies',
		joinColumn: { name: 'role_id' },
		inverseJoinColumn: { name: 'policy_id' },
	})
	policies: Policy[];

	isSuperAdmin() {
		return this.tags?.indexOf('SuperAdmin') >= 0;
	}

	isOrgAdmin() {
		return this.tags?.indexOf('OrgAdmin') >= 0;
	}

	isAgentBased() {
		return this.tags?.indexOf('AIAgent') >= 0;
	}

	isBotAdmin() {
		return this.tags?.indexOf('AIAgentAdmin') >= 0;
	}

	isNetomiSupport() {
		return this.tags?.indexOf('Support') >= 0;
	}

	isBotObserver() {
		return this.tags?.indexOf('AIAgent') >= 0 && this.tags?.indexOf('Observer') >= 0;
	}

	isDefault() {
		return this.tags?.indexOf('Default') >= 0;
	}
}
