import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { Role } from './role.entity';
import { BotSocialConfig } from './bot-social-config.entity';

@Entity('passport.bot_roles')
export class BotRole {
	@PrimaryGeneratedColumn({
		unsigned: true,
	})
	id: number;

	@Column({
		name: 'bot_id',
	})
	botId: string;

	@Column({
		name: 'bot_ref_id',
	})
	botRefId: string;

	@Column({
		name: 'role_id',
	})
	roleId: number;

	@Column({
		name: 'active',
	})
	active: number;

	@Column({
		name: 'created_by',
	})
	createdBy: number;

	@Column({
		name: 'updated_by',
	})
	updatedBy: number;

	@CreateDateColumn({
		name: 'created_at',
		type: 'timestamp',
	})
	createdAt: Date;

	@UpdateDateColumn({
		name: 'updated_at',
		type: 'timestamp',
	})
	updatedAt: Date;

	@OneToMany(() => BotSocialConfig, (bsc) => bsc.botId)
	botRefs: BotSocialConfig[];

	@OneToMany(() => Role, (roles) => roles.id)
	roles: Role[];
}
