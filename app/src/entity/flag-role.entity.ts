import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, JoinColumn, ManyToOne } from 'typeorm';
import { FeatureFlag } from './flag.entity';
import { OrgDivision } from './org-division.entity';
import { Org } from './org.entity';
import { BotSocialConfig } from './bot-social-config.entity';
import { Bot } from './bot.entity';
import { User } from './user.entity';
import { FlagVariant } from './flag-variant.entity';

@Entity('ff_roles')
export class FlagRole {
	@PrimaryGeneratedColumn({
		unsigned: true,
	})
	id: number;

	@Column({ name: 'flag_id', unsigned: true })
	flagId: number;

	@Column({ name: 'org_id', unsigned: true })
	orgId: number;

	@Column({ name: 'org_division_id', unsigned: true })
	orgDivisionId: number;

	@Column({ name: 'user_id', unsigned: true })
	userId: number;

	@Column({ name: 'bot_id' })
	botId: string;

	@Column({ name: 'bot_ref_id' })
	botRefId: string;

	@Column({ name: 'service_name' })
	serviceName: string;

	@Column({ name: 'env' })
	env: string;

	@Column({ name: 'version' })
	version: string;

	@Column({ name: 'on_variant' })
	onVariant: number;

	@Column({ name: 'negate' })
	negate: number;

	@Column('simple-json')
	settings: any;

	@Column({ name: 'status' })
	status: string;

	@Column({ name: 'active' })
	active: number;

	@Column({ name: 'created_by' })
	createdBy: number;

	@Column({ name: 'updated_by' })
	updatedBy: number;

	@CreateDateColumn({
		name: 'created_at',
		type: 'timestamp',
	})
	createdAt: Date;

	@UpdateDateColumn({
		name: 'updated_at',
		type: 'timestamp',
	})
	updatedAt: Date;

	@ManyToOne(() => FeatureFlag, (flag) => flag.flagRoles)
	@JoinColumn({ name: 'flag_id', referencedColumnName: 'id' })
	flag: FeatureFlag;

	@ManyToOne(() => User, (user) => user.flagRoles)
	@JoinColumn({ name: 'user_id' })
	user: User;

	@ManyToOne(() => Org, (org) => org.flagRoles)
	@JoinColumn({ name: 'org_id' })
	org: Org;

	@ManyToOne(() => OrgDivision, (orgDivision) => orgDivision.flagRoles)
	@JoinColumn({ name: 'org_division_id' })
	orgDivision: OrgDivision;

	@ManyToOne(() => Bot, (bot) => bot.flagRoles)
	@JoinColumn({ name: 'bot_id', referencedColumnName: 'botId' })
	bot: Bot;

	@ManyToOne(() => BotSocialConfig, (botRef) => botRef.flagRoles)
	@JoinColumn({ name: 'bot_ref_id', referencedColumnName: 'botRefId' })
	botRef: BotSocialConfig;

	@ManyToOne(() => FlagVariant, (ffv) => ffv.onRoles, { nullable: true })
	@JoinColumn({ name: 'on_variant', referencedColumnName: 'varIndex' })
	onValue: FlagVariant;
}
