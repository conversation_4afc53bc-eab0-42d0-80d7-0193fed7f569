import { <PERSON>tity, PrimaryGeneratedColumn, Column, CreateDateColumn, OneToOne, JoinColum<PERSON> } from 'typeorm';
import { StudioUser } from './studio-user.entity';

@Entity('user_audit')
export class UserAudit {
	@PrimaryGeneratedColumn({
		unsigned: true,
	})
	id: number;

	@Column({
		name: 'trace_id',
		type: 'varchar',
		nullable: true,
	})
	traceId: string | null;

	@Column()
	action: string;

	@Column()
	type: string;

	@Column({
		name: 'user_id',
		type: 'integer',
		nullable: true,
	})
	userId: number | null;

	@Column({
		name: 'uuid',
		type: 'varchar',
		nullable: true,
	})
	uuid: string | null;

	@Column({
		name:'first_name',
		type:'varchar',
		nullable: true,
	})
	firstName: string | null;

	@Column({
		name:'last_name',
		type:'varchar',
		nullable: true,
	})
	lastName: string | null;

	@Column({
		name: 'email',
		type: 'varchar',
		nullable: true,
	})
	email: string | null;

	@Column({
		name: 'org_id',
		type: 'integer',
		nullable: true,
	})
	orgId: number | null;

	@Column({
		name: 'org_division_id',
		type: 'integer',
		nullable: true,
	})
	orgDivisionId: number | null;

	@Column({
		name: 'bot_id',
		type: 'varchar',
		nullable: true,
	})
	botId: string | null;

	@Column({
		name: 'bot_ref_id',
		type: 'varchar',
		nullable: true,
	})
	botRefId: string | null | undefined;

	@Column('simple-json')
	notes: any;

	@Column('simple-json', {
		name: 'request_notes',
	})
	requestNotes: any;

	@Column({
		name: 'api_key',
		type: 'varchar',
		nullable: true,
	})
	apiKey: string | null;

	@Column({
		name: 'created_by',
	})
	createdBy: number;

	@CreateDateColumn({
		name: 'created_at',
		type: 'timestamp',
	})
	createdAt: Date;

	@OneToOne(() => StudioUser)
	@JoinColumn({
		name: 'uuid',
		referencedColumnName: 'id',
	})
	studioUser: StudioUser;
}
