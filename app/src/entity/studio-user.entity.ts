import { <PERSON>umn, CreateDateColumn, Entity, JoinColumn, OneToOne, PrimaryColumn, UpdateDateColumn } from 'typeorm';
import { User } from './user.entity';

@Entity('aistudio.users')
export class StudioUser {
	@PrimaryColumn()
	id: string;

	@Column({
		name: 'first_name',
	})
	firstName: string;

	@Column({
		name: 'last_name',
	})
	lastName: string;

	@Column({
		name: 'email',
	})
	email: string;

	@Column({
		name: 'status',
	})
	status: string;

	@Column({
		name: 'username',
	})
	username: string;

	@Column({
		name: 'type',
	})
	type: string;

	@Column({
		name: 'activated',
	})
	activated: number;

	@Column({
		name: 'login_count',
	})
	loginCount: number;

	@Column({
		name: 'failed_attempts',
	})
	failedAttempts: number;

	@CreateDateColumn({
		name: 'created_at',
		type: 'timestamp',
	})
	createdAt: Date;

	@UpdateDateColumn({
		name: 'updated_at',
		type: 'timestamp',
	})
	updatedAt: Date;

	@Column({
		name: 'activation_expiry_time',
		type: 'timestamp',
	})
	activationExpiryTime: Date;

	@Column({
		name: 'reset_expiry_time',
		type: 'timestamp',
	})
	resetExpiryTime: Date;

	@OneToOne(() => User, (u) => u.studioUser)
	@JoinColumn({ name: 'id', referencedColumnName: 'uuid' })
	passport?: User;
}
