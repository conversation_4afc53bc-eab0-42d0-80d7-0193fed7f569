import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { FeatureFlag } from './flag.entity';
import { FlagRole } from './flag-role.entity';

@Entity('ff_variants')
export class FlagVariant {
	@PrimaryGeneratedColumn({
		unsigned: true,
	})
	id: number;

	@Column({ name: 'flag_id' })
	flagId: number;

	@Column({ name: 'var_index' })
	varIndex: number;

	@Column({ name: 'name' })
	name: number;

	@Column({ name: 'value' })
	value: string;

	@Column({ name: 'active' })
	active: number;

	@Column({ name: 'created_by' })
	createdBy: number;

	@Column({ name: 'updated_by' })
	updatedBy: number;

	@CreateDateColumn({
		name: 'created_at',
		type: 'timestamp',
	})
	createdAt: Date;

	@UpdateDateColumn({
		name: 'updated_at',
		type: 'timestamp',
	})
	updatedAt: Date;

	@OneToMany(() => FeatureFlag, (flag) => flag.defaultValue)
	defaultFlags: FeatureFlag[];

	@OneToMany(() => FeatureFlag, (flag) => flag.offValue)
	offFlags: FeatureFlag[];

	@OneToMany(() => FlagRole, (fr) => fr.onValue)
	onRoles: FlagRole[];
}
