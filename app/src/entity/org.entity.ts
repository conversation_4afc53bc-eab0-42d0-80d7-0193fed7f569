import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, OneToOne, JoinColumn } from 'typeorm';
import { OrgDivision } from './org-division.entity';
import { User } from './user.entity';
import { SsoConfig } from './sso-config.entity';
import { Bot } from './bot.entity';
import { FlagRole } from './flag-role.entity';

@Entity('orgs')
export class Org {
	@PrimaryGeneratedColumn({
		unsigned: true,
	})
	id: number;

	@Column()
	name: string;

	@Column()
	type: string;

	@Column({
		name: 'sso_config_id',
	})
	ssoConfigId: number;

	@Column('simple-json')
	settings: {
		logoUrl: string;
		approvedDomains: [];
		orgDomains: [];
	};

	@Column('simple-json')
	policy: string;

	@Column()
	active: number;

	@Column({
		name: 'created_by',
	})
	createdBy: number;

	@Column({
		name: 'updated_by',
	})
	updatedBy: number;

	@CreateDateColumn({
		name: 'created_at',
		type: 'timestamp',
	})
	createdAt: Date;

	@UpdateDateColumn({
		name: 'updated_at',
		type: 'timestamp',
	})
	updatedAt: Date;

	@OneToMany(() => OrgDivision, (orgDivisions) => orgDivisions.org)
	orgDivisions: OrgDivision[];

	@OneToMany(() => User, (users) => users.org)
	users: User[];

	@OneToMany(() => Bot, (bots) => bots.org)
	bots: Bot[];

	@OneToOne(() => SsoConfig, (config) => config.org)
	@JoinColumn({ name: 'sso_config_id' })
	ssoConfig: SsoConfig;

	@OneToMany(() => FlagRole, (flagRole) => flagRole.org)
	flagRoles: FlagRole[];

	getLogoUrl(): string {
		return this.settings ? this.settings['logoUrl'] : '';
	}

	getApprovedDomains(): string[] {
		return this.settings ? this.settings['approvedDomains'] : [];
	}

	getOrgDomains(): string[] {
		return this.settings ? this.settings['orgDomains'] : [];
	}

	isPartner(): boolean {
		return this.type === 'Partner';
	}

	isProvider(): boolean {
		return this.type === 'Provider';
	}

	isClient(): boolean {
		return this.type === 'Client';
	}

	isPreSales(): boolean {
		return this.type === 'Pre-Sales';
	}

	logoUrl: string;
}
