import { <PERSON><PERSON><PERSON>, CreateDate<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { Role } from './role.entity';
import { User } from './user.entity';
import { Org } from './org.entity';
import { Bot } from './bot.entity';

/** Many-To-Many between Users vs Roles */
@Entity('user_roles')
export class UserRole {
	@PrimaryGeneratedColumn({ unsigned: true })
	id: number;

	@Column({ name: 'user_id' })
	userId: number;

	@Column({ name: 'uuid' })
	uuid: string;

	@Column({ name: 'role_id' })
	roleId: number;

	@Column({ name: 'org_id', type: 'integer', nullable: true })
	orgId: number | null | undefined;

	@Column({ name: 'org_division_id', type: 'integer', nullable: true })
	orgDivisionId!: number | null | undefined;

	@Column({ name: 'bot_id', type: 'varchar', nullable: true })
	botId!: string | null | undefined;

	@Column('simple-json', { name: 'settings' })
	settings: string;

	@Column('simple-json', { name: 'bots' })
	bots: {
		statements: {
			allow: {};
			deny: {};
		};
	};

	@Column()
	active: number;

	@Column({ name: 'created_by' })
	createdBy: number;

	@Column({ name: 'updated_by' })
	updatedBy: number;

	@CreateDateColumn({ name: 'created_at', type: 'timestamp' })
	createdAt: Date;

	@UpdateDateColumn({ name: 'updated_at', type: 'timestamp' })
	updatedAt: Date;

	@ManyToOne(() => User, (user) => user.userRoles)
	@JoinColumn({ name: 'user_id' })
	user: User;

	@ManyToOne(() => Role, (role) => role.userRoles)
	@JoinColumn({ name: 'role_id' })
	role: Role;

	@ManyToOne(() => Org, (org) => org.id)
	@JoinColumn({ name: 'org_id' })
	org: Org;

	@ManyToOne(() => Bot, (bot) => bot.userRoles, { nullable: true })
	@JoinColumn({ name: 'bot_id', referencedColumnName: 'botId' })
	bot: Bot;

	// Attributes required for syncing user roles and auditing
	syncAction?: string;
	syncText?: string;
	roleTags?: string;
}
