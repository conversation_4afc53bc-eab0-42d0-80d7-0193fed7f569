import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, JoinColumn, ManyToOne, OneToMany } from 'typeorm';
import { Bot } from './bot.entity';
import { FlagRole } from './flag-role.entity';

@Entity('studio.bot_social_config')
export class BotSocialConfig {
	@PrimaryGeneratedColumn()
	id: number;

	@Column({
		name: 'bot_id',
	})
	botId: string;

	@Column({
		name: 'bot_ref_id',
	})
	botRefId: string;

	@Column({
		name: 'name',
	})
	name: string;

	@Column({
		name: 'description',
	})
	description: string;

	@Column({
		name: 'channel_id',
	})
	channelId: string;

	@Column({
		name: 'social_channel',
	})
	socialChannel: string;

	@Column({
		name: 'env',
	})
	env: string;

	@Column({
		name: 'created_by',
	})
	createdBy: number;

	@Column({
		name: 'updated_by',
	})
	updatedBy: number;

	@CreateDateColumn({
		name: 'created_at',
		type: 'timestamp',
	})
	createdAt: Date;

	@UpdateDateColumn({
		name: 'updated_at',
		type: 'timestamp',
	})
	updatedAt: Date;

	@ManyToOne((_type) => Bot, (bot) => bot.botRefs)
	@JoinColumn({ name: 'bot_id', referencedColumnName: 'botId' })
	bot: Bot;

	@OneToMany(() => FlagRole, (flagRole) => flagRole.botRef)
	flagRoles: FlagRole[];
}
