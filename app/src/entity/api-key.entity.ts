import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn } from 'typeorm';

@Entity('api_keys')
export class ApiKey {
	@PrimaryGeneratedColumn({
		unsigned: true,
	})
	id: number;

	// UUID of Bot or User
	@Column()
	uuid: string;

	// Bot | User
	@Column()
	type: string;

	@Column({
		name: 'api_key',
	})
	apiKey: string;

	@Column({
		name: 'description',
	})
	description: string;

	@CreateDateColumn({
		name: 'expires',
		type: 'timestamp',
	})
	expires: Date;

	@CreateDateColumn({
		name: 'deactivated_at',
		type: 'timestamp',
	})
	deactivatedAt: Date;

	@Column({
		name: 'active',
	})
	active: number;

	@Column({
		name: 'created_by',
	})
	createdBy: number;

	@Column({
		name: 'updated_by',
	})
	updatedBy: number;

	@CreateDateColumn({
		name: 'created_at',
		type: 'timestamp',
	})
	createdAt: Date;

	@CreateDateColumn({
		name: 'updated_at',
		type: 'timestamp',
	})
	updated_at: Date;
}
