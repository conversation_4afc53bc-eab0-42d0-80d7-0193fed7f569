import { Column, CreateDateColumn, Entity, JoinTable, ManyToMany, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { Role } from './role.entity';

@Entity('policies')
export class Policy {
	@PrimaryGeneratedColumn({
		unsigned: true,
	})
	id: number;

	@Column({
		name: 'name',
	})
	name: string;

	@Column({
		name: 'description',
	})
	description: string;

	@Column({
		name: 'tags',
	})
	tags: string;

	@Column({
		name: 'active',
	})
	active: number;

	@Column({
		name: 'version',
	})
	version: string;

	@Column('simple-json', {
		name: 'policy',
	})
	policy: {
		version: string;
		statements: [];
	};

	@Column({
		name: 'created_by',
	})
	createdBy: number;

	@Column({
		name: 'updated_by',
	})
	updatedBy: number;

	@CreateDateColumn({
		name: 'created_at',
		type: 'timestamp',
	})
	createdAt: Date;

	@UpdateDateColumn({
		name: 'updated_at',
		type: 'timestamp',
	})
	updatedAt: Date;

	@ManyToMany((_type) => Role)
	@JoinTable({
		name: 'role_policies',
		joinColumn: { name: 'policy_id' },
		inverseJoinColumn: { name: 'role_id' },
	})
	roles: Role[];
}
