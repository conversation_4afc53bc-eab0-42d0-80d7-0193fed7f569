import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, OneToMany } from 'typeorm';

import { Org } from './org.entity';
import { FlagRole } from './flag-role.entity';

@Entity('org_divisions')
export class OrgDivision {
	@PrimaryGeneratedColumn({
		unsigned: true,
	})
	id: number;

	@Column({
		name: 'org_id',
		unsigned: true,
	})
	orgId: number;

	@Column()
	name: string;

	@Column()
	type: string;

	@Column()
	description: string;

	@Column('simple-json')
	settings: string;

	@Column()
	active: number;

	@Column({
		name: 'created_by',
	})
	createdBy: number;

	@Column({
		name: 'updated_by',
	})
	updatedBy: number;

	@CreateDateColumn({
		name: 'created_at',
		type: 'timestamp',
	})
	createdAt: Date;

	@UpdateDateColumn({
		name: 'updated_at',
		type: 'timestamp',
	})
	updatedAt: Date;

	@ManyToOne((_type) => Org, (org) => org.orgDivisions)
	@JoinColumn({ name: 'org_id' })
	org: Org;

	@OneToMany(() => FlagRole, (flagRole) => flagRole.orgDivision)
	flagRoles: FlagRole[];
}
