import { Injectable, OnModuleDestroy, OnModuleInit } from '@nestjs/common';
import { Kafka, Producer } from 'kafkajs';
import * as log from '../utils/log';
import { AuditMessageType } from '../auth/types';

@Injectable()
export class KafkaService implements OnModuleInit, OnModuleDestroy {
	private kafka: Kafka;
	private producer: Producer;
	private topic: string;

	constructor() {
		this.kafka = new Kafka({
			clientId: process.env.FFS_KAFKA_CLIENT_ID,
			brokers: process.env.FFS_KAFKA_HOST?.split(',') || [],
		});

		this.producer = this.kafka.producer();
		this.topic = process.env.KAFKA_USER_AUDIT_TOPIC_ID || 'passport-user-audit';
	}

	async onModuleInit() {
		await this.producer.connect();
		log.enter('KafkaService', 'onModuleInit');
	}

	async sendAuditMessage(message: AuditMessageType) {
		if (message) {
			await this.producer.send({
				topic: this.topic,
				messages: [{ key: message.flagName, value: JSON.stringify(message) }],
			});
			log.info('KafkaService', 'sendAuditMessage', this.topic, message.flagName);
		}
	}

	async onModuleDestroy() {
		await this.producer.disconnect();
		log.exit('FeatureFlagService', 'KafkaService', 'onModuleDestroy');
	}
}
