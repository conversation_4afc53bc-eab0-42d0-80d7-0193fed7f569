import { Controller, Get } from '@nestjs/common';
import { AppService } from './app.service';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

@Controller()
@ApiTags('Application')
export class AppController {
	constructor(private readonly appService: AppService) {}

	@Get('/api/version')
	@ApiOperation({ summary: 'Returns feature-flag-service version' })
	getVersion() {
		return this.appService.version();
	}

	@Get('/api/health')
	@ApiOperation({ summary: 'Check if server is currently running' })
	isAlive() {
		return { status: 'Server is running' };
	}
}
