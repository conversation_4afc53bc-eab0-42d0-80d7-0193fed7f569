import { Modu<PERSON> } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';

import { WebSocketModule } from '../websocket/websocket.module';
import { MulterModule } from '@nestjs/platform-express';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { UserService } from '../user/user.service';
import { BotService } from '../bot/bot.service';
import { DataSource } from 'typeorm';
import * as log from '../utils/log';
import { AppDataSource } from '../db/app-data-source';
import { FlagModule } from '../flag/flag.module';
import { AuthModule } from '../auth/auth.module';
import { KafkaModule } from '../kafka/kafka.module';

@Module({
	imports: [
		AuthModule,
		FlagModule,
		MulterModule.register({
			dest: './uploads',
		}),
		EventEmitterModule.forRoot(),
		WebSocketModule,
		KafkaModule,
	],
	controllers: [AppController],
	providers: [AppService, UserService, BotService],
})
export class AppModule {
	private dataSource: DataSource;

	async initDatasource() {
		log.enter('AppModule:initDatasource');

		try {
			this.dataSource = AppDataSource.getInstance();
			if (this.dataSource.isInitialized) {
				log.debug('AppModule:Datasource already initialized');
				this.dataSource = AppDataSource.getInstance();
				return;
			}
			await AppDataSource.getInstance().initialize();
		} catch (e) {
			log.error('AppModule:Failed to initialize Datasource', e);
			throw e;
		}
		log.exit('AppModule:Datasource successfully initialized');
	}

	constructor() {
		this.initDatasource();
	}
}
