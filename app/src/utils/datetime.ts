import { parse, format, formatDistanceToNow, parseISO } from 'date-fns';

const ISO_PATTERN = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'";
const UTC_PATTERN = 'yyyy-MM-dd HH:mm:ss';
const SIMPLE_DATE_PATTERN = 'yyyy-MM-dd';
const SIMPLE_24HR_PATTERN = 'HH:mm:ss';

export function diff(date1: Date, date2: Date) {
	return date2.getTime() - date1.getTime();
}

export function diffFromNow(date1: Date) {
	return new Date().getTime() - date1.getTime();
}

export function diffFromNowByString(date1: string): number {
	return new Date().getTime() - new Date(date1).getTime();
}

export function toUTC(unixTimestamp: number): string {
	return format(new Date(unixTimestamp), UTC_PATTERN);
}

export function unixTsToDate(unixTimestamp: number): string {
	return format(new Date(unixTimestamp), SIMPLE_DATE_PATTERN);
}

export function unixTsToTime(unixTimestamp: number): string {
	return format(new Date(unixTimestamp), SIMPLE_24HR_PATTERN);
}

export function unixTsByPattern(unixTimestamp: number, pattern = ISO_PATTERN) {
	return format(new Date(unixTimestamp), pattern);
}

export function fromString(dateString: string, fromPattern: string) {
	return parse(dateString, fromPattern, new Date());
}

export function formatDate(dateString: string, fromPattern: string, toPattern: string) {
	return format(fromString(dateString, fromPattern), toPattern);
}

export function daysAgo(date: Date): number | string {
	return formatDistanceToNow(date, { addSuffix: true });
}

export function fromISO(dateTimeString: string) {
	return parseISO(dateTimeString);
}

export function toString(date: Date, toPattern: string) {
	return format(date, toPattern);
}

export function toISO(date: Date) {
	return format(date, ISO_PATTERN);
}

export function nowAsUTC() {
	return toUTC(new Date().getTime());
}
