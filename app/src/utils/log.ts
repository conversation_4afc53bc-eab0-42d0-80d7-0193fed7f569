export function enter(msg: string, ...args: any) {
	console.log('ENTER:➡️', msg, ...args);
}

export function exit(msg: string, ...args: any) {
	console.log('EXIT:⬅️', msg, ...args);
}

export function info(msg: string, ...args: any) {
	console.info('INFO:', msg, ...args);
}

export function warn(msg: string, ...args: any) {
	console.warn('WARN:', msg, ...args);
}

export function error(msg: string, ...args: any) {
	console.error('ERROR:', msg, ...args);
}

export function debug(msg: string, ...args: any) {
	console.debug('DEBUG:', msg, ...args);
}

export function json(title: string, data: any, space = 0) {
	const jsonType = Array.isArray(data) ? `[${data.length}]` : '{:}';
	console.log(`JSON: ${title} ${jsonType}\n`, JSON.stringify(data, undefined, space));
}

export function result(msg: any) {
	const text = `${msg.exitcode}\t${msg.status}\t${msg.message}`;
	info(text);
}

export function enterx(service: string, method: string, context: any, ...args: any) {
	console.log(`ENTER ➡️: ${service}:${method}`, ...args, `author=${context?.uuid || context?.email}, traceId=${context?.traceId || ''}`);
	console.time(`${service}:${method}`);
}

export function exitx(service: string, method: string, context: any, ...args: any) {
	console.log(`EXIT⬅️: ${service}:${method}`, ...args, `author=${context?.uuid || context?.email}, traceId=${context?.traceId || ''}`);
	console.timeEnd(`${service}:${method}`);
}

export function infox(service: string, method: string, context: any, ...args: any) {
	console.info(`INFO: ${service}:${method}`, ...args, `author=${context?.uuid || context?.email}, traceId=${context?.traceId || ''}`);
}

export function warnx(service: string, method: string, context: any, ...args: any) {
	console.warn('WARN:', `${service}:${method}`, ...args, `author=${context?.uuid || context?.email}, traceId=${context?.traceId || ''}`);
}

export function errorx(service: string, method: string, context: any, ...args: any) {
	console.error('ERROR:', `${service}:${method}`, ...args, `author=${context?.uuid || context?.email}, traceId=${context?.traceId || ''}`);
}

export function jsonx(service: string, method: string, context: any, title: string, data: any, space = 0) {
	const jsonType = Array.isArray(data) ? `[${data.length}]` : '{:}';
	console.log(`JSON: ${service}:${method} author=${context?.uuid || context?.email}, traceId=${context?.traceId || ''}`, `${title} ${jsonType}\n`, JSON.stringify(data, undefined, space));
}

export function table(title: string, json: any) {
	console.info(title);
	console.table(json);
}

export function tableFormatted(title: string, headerLine: string, columnSize: string, msgs: Array<String>) {
	info(title);
	let data: string[] = [];
	const headers: any[] = headerLine.split(',');
	const pads: any[] = columnSize.split(',');
	data.push(headers.map((x, i) => x.padEnd(pads[i], ' ')).join('\t'));
	for (const msg of msgs) {
		let cols: string[] = [];
		for (let i = 0; i < headers.length; i++) {
			if (msg.hasOwnProperty(headers[i])) {
				cols.push(('' + msg[headers[i]]).padEnd(pads[i], ' '));
			} else {
				cols.push(''.padEnd(pads[i], ' '));
			}
		}
		data.push(cols.join('\t'));
	}
	console.log(data.join('\n'));
}

// function main() {
//     enter("main()")
//     warn("This is a warning!", "Please verify parameters")
//     info("This is an information text!", "Further details see webpage")
//     error("This is an error text", "status: 500")
//     debug("This is a debug note", "Need to investigate")
//     table2("Title", 'name,age,city', '10,5,20',
//		[{ name: "James B", age: 20, city: "New York" },
//       { name: "Hercule P", age: 15, city: "SFO" }, { name: 'Agatha C', age: 33, city: 'London' }])
//     table("Roster", [{ name: "James B", age: 20, city: "New York" },
//                      { name: "Hercule P", age: 15, city: "SFO" }, { name: 'Agatha C', age: 33, city: 'London' }])
//     exit("main()")
// }

// main()
