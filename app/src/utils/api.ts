import { ApiProperty, ApiResponse } from '@nestjs/swagger';
import { Request } from 'express';
import * as log from './log';
import * as dt from './datetime';

export interface ApiMeta {
	url: string;
	purpose: string;
	status?: number;
	requestorId?: string;
	traceId?: string;
	requestTime?: string;
	timeTakenMs?: number;
	successMessage?: string;
	isError?: boolean;
	errorMessage?: string;
	suggestions?: Array<string>;
	count?: number;
	paginated?: {
		total: number;
		limit: number;
		page: number;
	};
}

export class ResponsePayloadType<T> {
	@ApiProperty({ description: 'Meta info about the API' })
	_meta: ApiMeta;
	@ApiProperty({ description: 'Response payload' })
	payload?: T | undefined | null;
}

export class ErrorResponseDTO {
	@ApiProperty({ description: 'Error to display it for user' })
	error: string;
}

export class AuthorizeRequestDto {
	@ApiProperty({
		description: 'Email of the user to be authorized.',
		example: '<EMAIL>',
	})
	email: string;
}

export class Api {
	// Authorize
	static postAuthorize: ApiMeta;
	static getPassportUser: any;

	// AuthGuard
	static authGuard: ApiMeta;

	// Flag
	static getFlags: ApiMeta;
	static postFlagProfile: ApiMeta;
	static evaluateFlag: ApiMeta;
	static getFlagVariants: ApiMeta;
	static putFlag: ApiMeta;
	static putFlagVariant: ApiMeta;
	static putFlagRole: ApiMeta;
	static searchFlags: ApiMeta;
	static getPaginatedFlagsWithRoles: ApiMeta;
	static importFlags: ApiMeta
	static postPaginatedFlagsWithRoles: ApiMeta;
	static deleteFlags: ApiMeta;
}

/* eslint-disable max-len */
Api.postAuthorize = { url: '/api/authorize', purpose: 'Authorize user by API Key', successMessage: 'Returns access token if authorized' };
Api.authGuard = { url: '/api/*', purpose: 'Authenticate access token', successMessage: 'User is authenticated' };

Api.getFlags = { url: '/api/flags', purpose: 'Get list of Feature Flags', successMessage: 'Returns a list of feature flags' };
Api.getPaginatedFlagsWithRoles = { url: '/api/paginated-flags', purpose: 'Get a paginated list of feature flags along with their associated flag roles', successMessage: 'Get a paginated list of feature flags along with their associated flag roles' };
Api.postPaginatedFlagsWithRoles = { url: '/api/paginated-flags', purpose: 'Get a paginated list of feature flags along with their associated flag roles', successMessage: 'Get a paginated list of feature flags along with their associated flag roles' };
Api.postFlagProfile = { url: '/api/flags', purpose: 'Returns a complete profile of the flag', successMessage: 'Full profile of the flag' };
Api.evaluateFlag = { url: '/api/evaluate', purpose: 'Evaluate list of Feature Flags or other params', successMessage: 'Evaluates a list of feature flag profiles' };
Api.getFlagVariants = { url: '/api/flag-variants/:flagIdentity', purpose: 'Returns all the variants for this flag', successMessage: 'All variants for this flag' };
Api.putFlag = { url: '/api/flag', purpose: 'Create a new Feature Flag or update an existing Flag', successMessage: 'A feature flag has been created or updated' };
Api.putFlagVariant = { url: '/api/flag-variant', purpose: 'Create a new Variant or update an existing variant for a given Flag', successMessage: 'A Flag Variant has been created or updated' };
Api.putFlagRole = {
	url: '/api/flag-role',
	purpose: 'Creates a relation of flag with an existing entity like User, Org, Bot or BotRef',
	successMessage: 'The flag has been associated with a known entity',
};
Api.searchFlags = { url: '/api/search', purpose: 'Searches for flags by given crietiera', successMessage: 'Returns a list of flags matched by the criteria' };
Api.importFlags = { url: '/api/flags/import', purpose: 'Import Flags across Different Environment', successMessage: 'Flags Imported Successfully' };
Api.deleteFlags = { url: '/api/flags/delete', purpose: 'delete Flags with its roles and variants', successMessage: 'Flags deleted Successfully' };

export const successResponse = (request: Request, apiMeta: ApiMeta, payload: any, status: number = 200) => {
	const requestTime = new Date(request.headers['date'] as string);
	const timeTakenMs = dt.diffFromNow(requestTime);
	log.info(`success: traceId=${request.traceId}, api=${apiMeta.url}, timeMs=${timeTakenMs}, author=${request.passportUser?.email || request.body?.email || payload?.email || ''}`);
	const meta = {
		...apiMeta,
		status,
		requestorId: request.passportUser?.uuid,
		traceId: request.traceId,
		requestTime: requestTime.toISOString(),
		timeTakenMs,
		isError: false,
	};
	if (payload instanceof Array) {
		meta.count = payload.length;
	}
	return {
		_meta: meta,
		payload,
	};
};

export const successResponseWithPaginatedResult = (request: Request, apiMeta: ApiMeta, payload: any, status: number = 200) => {
	const requestTime = new Date(request.headers['date'] as string);
	const timeTakenMs = dt.diffFromNow(requestTime);
	log.info(`success: traceId=${request.traceId}, api=${apiMeta.url}, timeMs=${timeTakenMs}, author=${request.passportUser?.email || request.body?.email || payload?.email || ''}`);
	const meta = {
		...apiMeta,
		status,
		requestorId: request.passportUser?.uuid,
		traceId: request.traceId,
		requestTime: requestTime.toISOString(),
		timeTakenMs,
		isError: false,
		count: payload.results.length,
		paginated: {
			total: payload.total,
			limit: payload.limit,
			page: payload.page,
		},
	};
	return {
		_meta: meta,
		payload: payload.results,
	};
};

export const successResponseForPaginatedFlags = (request: Request, apiMeta: ApiMeta, payload: any, extraMeta?: { lastReturnedIndex?: number; totalFlagCount?: number }, status: number = 200) => {
	const requestTime = new Date(request.headers['date'] as string);
	const timeTakenMs = dt.diffFromNow(requestTime);

	log.info(`success: traceId=${request.traceId}, api=${apiMeta.url}, timeMs=${timeTakenMs}, author=${request.passportUser?.email || request.body?.email || ''}`);

	const meta: ApiMeta = {
		...apiMeta,
		status,
		requestorId: request.passportUser?.uuid,
		traceId: request.traceId,
		requestTime: requestTime.toISOString(),
		timeTakenMs,
		isError: false,
		count: Array.isArray(payload) ? payload.length : undefined,
		...extraMeta,
	};

	return {
		_meta: meta,
		payload,
	};
};

export const failureResponse = (request: Request, apiMeta: ApiMeta, errorMessage: string, payload: any = null, status: number = 500, suggestions: Array<string> = []): ResponsePayloadType<any> => {
	const requestTime = new Date(request.headers['date'] as string);
	const timeTakenMs = dt.diffFromNow(requestTime);
	log.error(
		`failed: traceId=${request.traceId}, api=${apiMeta.url}, timeMs=${timeTakenMs}, error=${errorMessage}, suggestions=${suggestions.join(',')}, author=${
			request.passportUser?.email || request.body?.email || payload?.email || ''
		}`,
	);
	return {
		_meta: {
			url: apiMeta.url,
			purpose: apiMeta.purpose,
			status,
			requestorId: request.passportUser?.uuid,
			traceId: request.traceId,
			requestTime: requestTime.toISOString(),
			timeTakenMs,
			isError: true,
			errorMessage,
			suggestions,
		},
		payload,
	};
};

export const ignorableResponse = (request: Request, apiMeta: ApiMeta, errorMessage: string, payload: any = null, status: number = 500, suggestions: Array<string> = []): ResponsePayloadType<any> => {
	const requestTime = new Date(request.headers['date'] as string);
	const timeTakenMs = dt.diffFromNow(requestTime);
	return {
		_meta: {
			url: apiMeta.url,
			purpose: apiMeta.purpose,
			status,
			requestorId: request.passportUser?.uuid,
			traceId: request.traceId,
			requestTime: requestTime.toISOString(),
			timeTakenMs,
			isError: true,
			errorMessage,
			suggestions,
		},
		payload,
	};
};

export const missingCredentialsResponse = (request: Request, apiMeta: ApiMeta) => {
	return failureResponse(request, apiMeta, 'Access Denied. Missing author credentials', null, 401, [
		'Are you sending a valid access token in Authorization Header?',
		'Call the /api/authorize API to get a new Access Token',
	]);
};

export const invalidCredentialsResponse = (request: Request, apiMeta: ApiMeta, payload: any = null): ResponsePayloadType<any> => {
	return failureResponse(request, apiMeta, 'Access Denied. Missing or invalid access token in Authorization header', payload, 401, [
		'Are you sending a valid access token in Authorization Header?',
		'Call the /api/authorize API to get a new Access Token',
	]);
};

export const inactiveLoginUser = (request: Request, apiMeta: ApiMeta, payload: any = null): ResponsePayloadType<any> => {
	return failureResponse(request, apiMeta, 'Access Denied. User is not found or inactive', payload, 401, ['Check with your Administrator if you have registered and activated correctly']);
};

export const forbiddenResponse = (request: Request, apiMeta: ApiMeta, errorMessage: string, payload: any = null, suggestions: Array<string> = []) => {
	return failureResponse(
		request,
		apiMeta,
		errorMessage,
		payload,
		403,
		suggestions.concat('You do not have access to this resource').concat('Check with your Administrator to provide required entitlements.'),
	);
};

export const notFoundResponse = (request: Request, apiMeta: ApiMeta, errorMessage: string, payload: any = null, suggestions: Array<string> = []) => {
	return failureResponse(request, apiMeta, errorMessage, payload, 404, suggestions.concat(['Please check if given query params are valid']));
};

export const tokenExpiredResponse = (request: Request, apiMeta: ApiMeta, errorMessage: string) => {
	return failureResponse(request, apiMeta, `Access Denied. Token is corrupt or expired; (${errorMessage})`, null, 401, [
		'Check if you are sending access token in Authorization Header',
		'Call the /api/authorize API to get a new Access Token',
	]);
};

export const incorrectParamFormatResponse = (request: Request, apiMeta: ApiMeta, errorMessage: string, suggestions: Array<string> = []) => {
	return failureResponse(request, apiMeta, `Incorrect Param Format: ${errorMessage}`, null, 400, suggestions);
};

export const missingRequiredParamResponse = (request: Request, apiMeta: ApiMeta, paramName: string, suggestions: Array<string> = []) => {
	return failureResponse(request, apiMeta, `Missing Required Param: ${paramName}`, null, 422, suggestions.concat(['Provide the required parameter value']));
};

export const unexpectedParamValueResponse = (request: Request, apiMeta: ApiMeta, errorMessage: string, suggestions: Array<string> = []) => {
	return failureResponse(request, apiMeta, `Unexpected Param Value: ${errorMessage}`, null, 422, suggestions);
};

export const nonPassportUserResponse = (request: Request, apiMeta: ApiMeta, errorMessage: string) => {
	return ignorableResponse(request, apiMeta, `Not a Passport user; (${errorMessage})`, null, 401, ['User is not enabled for Passport yet']);
};

export function ApiResponse400() {
	return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
		ApiResponse({ status: 400, description: 'Missing or Invalid Parameter(s)', type: ResponsePayloadType })(target, propertyKey, descriptor);
	};
}

export function ApiResponse401() {
	return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
		ApiResponse({ status: 401, description: 'Access token not provided or expired.', type: ErrorResponseDTO })(target, propertyKey, descriptor);
	};
}

export function ApiResponse403() {
	return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
		ApiResponse({ status: 403, description: 'Access forbidden to this resource', type: ResponsePayloadType })(target, propertyKey, descriptor);
	};
}

export function ApiResponse404() {
	return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
		ApiResponse({ status: 404, description: 'Some entity was not found.', type: ErrorResponseDTO })(target, propertyKey, descriptor);
	};
}

export function ApiResponse500() {
	return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
		ApiResponse({ status: 500, description: 'Failed operation.', type: ResponsePayloadType })(target, propertyKey, descriptor);
	};
}

export function DefaultErrorsResponse() {
	return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
		ApiResponse400()(target, propertyKey, descriptor);
		ApiResponse404()(target, propertyKey, descriptor);
		ApiResponse401()(target, propertyKey, descriptor);
	};
}
