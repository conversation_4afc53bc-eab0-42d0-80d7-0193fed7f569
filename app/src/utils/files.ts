import csv from 'csvtojson';
import fse from 'fs-extra';
import jsonexport from 'jsonexport';
import { ungzip } from 'pako';
import * as log from './log';

export async function readLocalFile(path: string, file: string) {
	const data = await fse.promises.readFile(`${path}/${file}`, 'utf-8');
	return data;
}

export function readCsvFile(path: string, file: string) {
	log.debug('readCsvFile', path, file);
	if (fse.existsSync(`${path}/${file}`)) {
		return fse.readFileSync(`${path}/${file}`, 'utf-8');
	}
	return null;
}

export function writeCsvFile(path: string, file: string, csvHeader: string | null, csvList: string[]) {
	log.debug('writeCsvFile', path, file);
	if (csvHeader !== null) {
		fse.outputFileSync(`${path}/${file}`, csvHeader + '\n');
		appendFileSync(path, file, csvList.join('\n'));
	} else {
		fse.outputFileSync(`${path}/${file}`, csvList.join('\n'));
	}
}

export function readTextFile(path: string, file: string) {
	log.debug('readTextFile', path, file);
	if (fse.existsSync(`${path}/${file}`)) {
		return fse.readFileSync(`${path}/${file}`, 'utf-8');
	}
	return null;
}

export function writeTextFile(path: string, file: string, text: string) {
	log.debug('writeTextFile', path, file);
	fse.outputFileSync(`${path}/${file}`, text);
}

export function readJSONFile(path: string, file: string) {
	log.debug('readJSONFile', path, file);
	const resolvedPath = `${path}/${file}`;
	if (fse.existsSync(resolvedPath)) {
		return JSON.parse(fse.readFileSync(resolvedPath) as any);
	}
	log.error('failed to resolve paths', resolvedPath);
	return null;
}

export function readJSONFiles(path: string) {
	log.debug('readJSONFile', path);
	const jsonList: any[] = [];
	if (fse.existsSync(path)) {
		const filenames = fse.readdirSync(path);
		for (const f of filenames) {
			if (f.endsWith('.json')) {
				const json: any = readJSONFile(path, f);
				console.log('reading: ', f);
				if (json) jsonList.push(json);
			}
		}
	}
	return jsonList;
}

export async function readCsvAsJSONFile(path: string, csvFileName: string) {
	const json = await csv().fromFile(`${path}/${csvFileName}`);
	// log.json("json", json)
	return json;
}

export async function writeJSONAsCsvFile(path: string, file: string, json: any, _options: any) {
	let defaultOptions = { lang: 'Node.js', module: 'jsonexport', forceTextDelimiter: true };
	let options = Object.assign({}, defaultOptions, _options);
	const csv = await jsonexport(json, options);
	// const csvHeader = Object.keys(json[0]).join(',');
	writeTextFile(path, file, csv);
}

// export async function writeJSONAsCsv(path: string, file: string, json: any, includeHeaders: boolean) {
//     const csv = jsonexport(json, {lang: 'Node.js', module: 'jsonexport', forceTextDelimiter: true, includeHeaders})
//     writeTextFile(path, file, csv)
// }

export function writeJSONFile(path: string, file: string, json: any) {
	log.debug('writeJSONFile', path, file);
	fse.outputFileSync(`${path}/${file}`, JSON.stringify(json, undefined, 2));
}

export function readConfig(key: string, file = 'config.json') {
	const p0 = process.cwd() + '/../../config';
	log.enter(`readConfig(${key})`, key, p0);
	return readJSONFile(p0, file)[key];
}

export function appendFileSync(path: string, file: string, data: any) {
	fse.appendFileSync(`${path}/${file}`, data);
}

export function fileExistsSync(path: string) {
	return fse.existsSync(path);
}

export function gunzip(data: any, _code = 'base64') {
	const buffer = Buffer.from(data);
	const binaryArray = ungzip(buffer);
	return String.fromCharCode.apply(null, binaryArray);
}

export function matchPattern(text: string, rx: string) {
	const regex = new RegExp(rx, 'gm');
	const result = regex.exec(text);
	// log.info("Result", result)
	return result;
}

export function unflatten(data: any): any {
	let result: any = {};
	for (let i in data) {
		let keys = i.split('.');
		keys.reduce((acc, value, index) => {
			return acc[value] || (acc[value] = isNaN(Number(keys[index + 1])) ? (keys.length - 1 === index ? data[i] : {}) : []);
		}, result);
	}
	return result;
}
