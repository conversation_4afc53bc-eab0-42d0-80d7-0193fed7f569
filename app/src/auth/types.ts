import { Bot } from '../entity/bot.entity';
import { User } from '../entity/user.entity';

export type JWTPayload = {
	userId: number;
	email: string;
	uuid: string;
	orgId: number;
};

export type JWTSignResponse = {
	token: string;
	userId: number;
	email: string;
	uuid: string;
	orgId: number;
};

// users table
export type UserResponse = {
	id: number;
	orgId: number;
	firstName: string;
	lastName: string;
	email: string;
	uuid: string | null;
	managerUserId?: number;
	active: number;
	settings?: any;
	policy?: any;
};
export class ValidationError extends Error {
	cause: string;
	constructor(message: string, cause: string = '') {
		super(message);
		this.name = 'ValidationError';
		this.cause = cause;
	}
}

export interface PaginatedResult<T> {
	results: Partial<T | undefined>[];
	page: number;
	limit: number;
	total: number;
}

export type AuditMessageType = {
	traceId?: string;
	flagName: string;
	action: string;
	userId?: number;
	uuid?: string;
	email?: string;
	orgId?: number;
	orgDivisionId?: number;
	botId?: string;
	botRefId?: string;
	loginIp?: string;
	loginAgent?: string;
	notes: any;
};

/**
 * Represents an actual "User" (User.entity, authenticated via OAuth/SAML)
 */
export type PassportUser = User;
export type PassportBot = Bot;
