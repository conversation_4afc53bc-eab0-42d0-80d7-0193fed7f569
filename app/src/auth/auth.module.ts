import { Module, forwardRef } from '@nestjs/common';

import { APP_GUARD } from '@nestjs/core';
import { JwtModule } from '@nestjs/jwt';
import { ConfigEnv } from '../init/config-env';
import { UserService } from '../user/user.service';
import { AuthGuard } from './auth-guard';
import { BotService } from '../bot/bot.service';

@Module({
	imports: [],
	providers: [
		{
			provide: ConfigEnv,
			useFactory: async () => {
				// Load ConfigEnv from the database or any other async operation
				const ce = ConfigEnv.getInstance();
				return ce;
			},
		},
	],
	exports: [ConfigEnv],
})
export class ConfigModule {}

@Module({
	imports: [
		forwardRef(() => ConfigModule),
		JwtModule.registerAsync({
			imports: [ConfigModule],
			useFactory: async (ce: ConfigEnv) => ({
				secret: ce.JWTSecret,
				signOptions: { expiresIn: '12h' },
			}),
			inject: [ConfigEnv],
		}),
	],
	providers: [
		UserService,
		BotService,
		{
			provide: APP_GUARD,
			useClass: AuthGuard,
		},
	],
})
export class AuthModule {}
