import { CanActivate, ExecutionContext, Injectable, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';
import * as log from '../utils/log';
import { ConfigEnv } from '../init/config-env';
import { JWTPayload } from './types';
import { AppErrors } from '../init/constants';
import * as uuid from 'uuid';
import { Api, invalidCredentialsResponse, nonPassportUserResponse, notFoundResponse, tokenExpiredResponse } from '../utils/api';
import { UserService } from '../user/user.service';
import { BotService } from '../bot/bot.service';

// Do not guard these Paths
export const WHITELIST = ['/api/version', '/api/health'];

export const WHITELIST_REGEX = [/^\/cacheclear\/(?:([^/]+?))/, /^\/api\/user\/([^/]+)\/active-roles-count$/];
export const SKIP_USER_FETCH = [/^\/sites\/(?:([^/]+?))\/view\/(?:([^/]+?))\/data\/?$/i];

@Injectable()
export class AuthGuard implements CanActivate {
	constructor(
		private jwtService: JwtService,
		private readonly userService: UserService,
		private readonly botService: BotService,
	) {}

	async canActivate(context: ExecutionContext): Promise<boolean> {
		const request = context.switchToHttp().getRequest() as Request;
		request.traceId = uuid.v4();
		request.headers['date'] = new Date().toISOString();
		try {
			// Step 1: Get Request
			log.enter('AuthGuard:', context.getHandler().name, request.path, `traceId=${request.traceId}`, request.headers['user-agent']);

			const ce = ConfigEnv.getInstance();
			let payload: any;

			// Step 1: Validate API Key
			const isValidApiKey = request.headers['x-passport-key'] === ce.get('PASSPORT_AUTH_KEY') && request.headers['x-passport-secret'] === ce.get('PASSPORT_AUTH_SECRET');
			if (isValidApiKey) {
				payload = request.body;
			} else {
				// Step 2: Extract Token from request header
				const token = this.extractTokenFromHeader(request);
				request.token = token || '';

				if (WHITELIST.some((p) => p === request.path)) {
					return true;
				}
				if (WHITELIST_REGEX.some((p) => p.test(request.path))) return true;

				if (!token) {
					throw new UnauthorizedException(invalidCredentialsResponse(request, Api.authGuard));
				}

				if (token?.toLowerCase().trim() === 'null') {
					throw new UnauthorizedException(nonPassportUserResponse(request, Api.authGuard, 'Invalid token'));
				}

				// Step 3: Verify JWT Payload
				try {
					// log.json("AuthGuard:jwt payload", request.body);
					payload = await this.jwtService.verifyAsync<JWTPayload>(token, {
						secret: ce.JWTSecret,
					});
					// log.json("AuthGuard:successfully verified jwt payload", payload);
				} catch (e) {
					// log.error('AuthGuard:failed to verify jwt payload:', request.traceId, e.message);
					throw new UnauthorizedException(tokenExpiredResponse(request, Api.authGuard, e.message));
				}

				// Step 4: Find Passport User or Passport Bot from db
				// A real-user can login to feature-flag-service via email + api-keys (UserEntitlement)
				// A Bot can login to feature-flag-service using botId + api-keys (BotEntitlement)
				const { email, botId } = payload;

				if (email) {
					const passportUser = await this.userService.findAuthor(email);
					if (!passportUser) {
						throw new NotFoundException(notFoundResponse(request, Api.authGuard, AppErrors.USER_NOT_FOUND, { email }));
					}
					passportUser.traceId = request.traceId;
					if (!passportUser.active) {
						throw new NotFoundException(notFoundResponse(request, Api.authGuard, AppErrors.USER_INACTIVE, { email }));
					}
					request.passportUser = passportUser;
				} else if (botId) {
					const bot = await this.botService.findLoginBot(botId);
					if (!bot) {
						throw new NotFoundException(notFoundResponse(request, Api.authGuard, AppErrors.BOT_NOT_FOUND));
					}
					if (bot.isDeleted === 1) {
						throw new NotFoundException(notFoundResponse(request, Api.authGuard, AppErrors.BOT_INACTIVE));
					}
					request.passportBot = bot;
				}
			}
		} catch (e) {
			throw e;
		}
		return true;
	}

	private extractTokenFromHeader(request: Request): string | undefined {
		const [type, token] = request.headers.authorization?.split(' ') ?? [];
		return type === 'Bearer' ? token : undefined;
	}
}
