import { WebSocketGateway as NestWebSocketGateway, WebSocketServer, OnGatewayConnection, OnGatewayDisconnect, SubscribeMessage } from '@nestjs/websockets';

import { Server, Socket } from 'socket.io';
import * as log from '../utils/log';
import { toUTC } from '../utils/datetime';
import { ConnectionService } from '../flag/connection.service';

@NestWebSocketGateway({ cors: { origin: '*' }, transports: ['websocket'] })
export class WebSocketGateway implements OnGatewayConnection, OnGatewayDisconnect {
	@WebSocketServer()
	server: Server;

	constructor(private readonly connectionService: ConnectionService) {}

	async onModuleInit() {
		log.enter('WebSocketGateway', 'onModuleInit');

		const previousConnections = await this.connectionService.findActiveConnections();

		for (const c0 of previousConnections) {
			log.info('WebSocketGateway', 'onModuleInit', `Restoring connection for clientId=${c0.clientId} [${c0.clientName}@${c0.clientIp})]`);

			// Emit a message to request the client to reconnect
			this.server.to(c0.clientId).emit('restoreConnection', {
				clientId: c0.clientId,
				clientName: c0.clientName,
				flags: await this.connectionService.findFlagsByClientId(c0.clientId),
			});
		}
	}

	getClientIdentity(client: Socket) {
		const clientId = client.id;
		const clientIp = client.handshake.address;
		const clientName = (client.handshake.query.clientName as string) || 'Unknown';
		const clientHost = client.handshake.headers['host'];
		const issuedTs = toUTC(client.handshake.issued);
		const clientLog = `id=${clientId}; name=${clientName}; ip=${clientIp}; host=${clientHost}; ts=${issuedTs}`;
		return { clientId, clientIp, clientName, clientHost, issuedTs, clientLog };
	}

	async handleConnection(client: Socket) {
		const ci0 = this.getClientIdentity(client);
		log.enter('WebSocketGateway', `🤝 handleConnection(${client.id})`, ci0.clientLog);
		await this.connectionService.saveConnection({
			clientId: ci0.clientId,
			clientName: ci0.clientName,
			clientIp: ci0.clientIp,
			clientHost: ci0.clientHost,
			status: 'connect',
			issuedTs: ci0.issuedTs,
			active: 1,
		});

		client.emit('message', `FFS:WebSocketGateway: (Helo) connection accepted from src=${ci0.clientLog}`);
		log.exit('WebSocketGateway', `handleConnection(); updated db ${ci0.clientId}`);
	}

	async handleDisconnect(client: Socket) {
		const ci0 = this.getClientIdentity(client);
		log.enter('WebSocketGateway', `👋 handleDisconnect(${client.id})`, ci0.clientLog);
		await this.connectionService.saveConnection({
			clientId: ci0.clientId,
			status: 'disconnect',
			active: 0,
		});
		log.exit('WebSocketGateway', `handleDisconnect(); updated db ${ci0.clientId}`);
	}

	@SubscribeMessage('subscribe-flags')
	async handleSubscribeFlags(client: Socket, flags: string) {
		const ci0 = this.getClientIdentity(client);
		log.enter('WebSocketGateway', '🔀 handleSubscribe', `Request to subscribe: ${ci0.clientLog}; flags=${flags}`);
		await this.waitForConnection(ci0.clientId);
		await this.connectionService.saveConnection({
			clientId: ci0.clientId,
			status: 'subscribe-flags',
			flags,
			active: 1,
		});
		// Use room-based subscriptions
		client.join(flags);
		log.exit('WebSocketGateway', '🔀 handleSubscribe', `Successfully subscribed to flags: ${ci0.clientLog}; flags=${flags}`);
	}

	async waitForConnection(clientId: string, maxRetries = 10, delayMs = 500) {
		for (let i = 0; i < maxRetries; i++) {
			log.info('WebSocketGateway', 'waitForConnection', `retry=${i + 1}`);
			const exists = await this.connectionService.connectionExists(clientId);
			if (exists) return;
			await new Promise((resolve) => setTimeout(resolve, delayMs)); // Wait before retrying
		}
		throw new Error(`Timeout: Client ${clientId} is not found in the database after a few tries. Try reconnecting.`);
	}

	updateFlag(name: string, value: any) {
		log.info(`📡 broadcasting flag-updated: ${name}=${value}`);
		this.server.to(name).emit('flagUpdated', { name, value });
	}
}
