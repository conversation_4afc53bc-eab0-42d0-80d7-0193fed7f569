import { Injectable } from '@nestjs/common';
import _ from 'lodash';
import NodeCache from 'node-cache';
import { RepositoryWrapper } from '../db/repository-wrapper';
import { Bot } from '../entity/bot.entity';
import * as log from '../utils/log';

@Injectable()
export class BotService extends RepositoryWrapper<Bot> {
	static nodeCache = new NodeCache({ stdTTL: 180, checkperiod: 90 });

	async findLoginBot(botId: string): Promise<Bot | null> {
		log.enter('BotService:', 'findLoginBot', botId);
		try {
			const bot = await this.dataSource
				.getRepository(Bot)
				.createQueryBuilder('bot')
				.leftJoinAndSelect('bot.botRefs', 'botRefs')
				.leftJoinAndSelect('bot.roles', 'role')
				.leftJoinAndSelect('role.policies', 'policies')
				.where('bot.botId = :botId', { botId })
				.getOne();

			return bot as Bot;
		} catch (e) {
			log.error('BotService:', 'findLoginBot', `Failed to find bot profile for botId: ${botId}`, e);
			throw e;
		}
	}
}
