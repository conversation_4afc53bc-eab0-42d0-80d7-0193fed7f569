/* eslint-disable import/first */
import { NestFactory } from '@nestjs/core';
// import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import compression from 'compression';
import dotenv from 'dotenv';
import express from 'express';
import session from 'express-session';
import path from 'path';
import 'reflect-metadata';
import { AppModule } from './app/app.module';
import { isProd, storageFolder } from './init/constants';
import { initServices } from './init/init-services';
import { serveSwaggerDocumentation } from './init/init-docs';
import * as log from './utils/log';
import { IoAdapter } from '@nestjs/platform-socket.io';

dotenv.config();

const domains = process.env.CORS_DOMAINS;

const corsOptions = {
	origin: domains ? domains.split(',') : '*',
};

async function start() {
	log.debug(`Staring feature-flag-service...`);
	log.debug('Current Dir', __dirname);
	log.debug('Storage folder: ', storageFolder);

	try {
		await initServices();

		const app = await NestFactory.create(AppModule);
		app.useWebSocketAdapter(new IoAdapter(app));
		app.enableCors(corsOptions);

		app.use(session({ secret: 'keyboard cat', resave: true, saveUninitialized: true }));
		app.use(compression());
		app.use('/static', express.static(path.join(__dirname, '../public')));
		if (!isProd) {
			serveSwaggerDocumentation(app);
		}
		const port = process.env.PORT || 4000;
		try {
			// await app.startAllMicroservices();
			await app.listen(port);
		} catch (error) {
			log.error('Failed to start feature-flag-service Nest server', error);
		}
		log.info(`🚀 Nest server is running on port ${port}`);
		return app;
	} catch (e) {
		log.error('Failed to start feature-flag-service app. Check if .env file is present. If not, execute "npm run setup-env" to get the config values.\n', e);
	}
	return null;
}

start();
