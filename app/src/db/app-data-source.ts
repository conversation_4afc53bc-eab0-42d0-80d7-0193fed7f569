import { DataSource } from 'typeorm';
import { ConfigEnv } from '../init/config-env';
import * as log from '../utils/log';
import { Api<PERSON>ey } from '../entity/api-key.entity';
import { BotRole } from '../entity/bot-role.entity';
import { BotSocialConfig } from '../entity/bot-social-config.entity';
import { Bot } from '../entity/bot.entity';
import { FlagRole } from '../entity/flag-role.entity';
import { FlagVariant } from '../entity/flag-variant.entity';
import { FeatureFlag } from '../entity/flag.entity';
import { OrgDivision } from '../entity/org-division.entity';
import { Org } from '../entity/org.entity';
import { Policy } from '../entity/policy.entity';
import { Resource } from '../entity/resource.entity';
import { RolePolicy } from '../entity/role-policy.entity';
import { Role } from '../entity/role.entity';
import { SsoConfig } from '../entity/sso-config.entity';
import { StudioUser } from '../entity/studio-user.entity';
import { Connection } from '../entity/connection.entity';
import { UserAudit } from '../entity/user-audit.entity';
import { UserRole } from '../entity/user-role.entity';
import { User } from '../entity/user.entity';

let instance: DataSource;
export class AppDataSource {
	static getInstance() {
		if (!instance) {
			const ce = ConfigEnv.getInstance();
			log.info(`initServices: attempting to connect to MySQL DB: ${ce.get('MYSQL_PASSPORT_DB_HOST')}`);
			instance = new DataSource({
				type: 'mysql',
				host: ce.get('MYSQL_PASSPORT_DB_HOST'),
				port: 3306,
				username: ce.get('MYSQL_PASSPORT_DB_USER'),
				password: ce.get('MYSQL_PASSPORT_DB_PASSWORD'),
				database: ce.get('MYSQL_PASSPORT_DB_NAME'),
				connectTimeout: 10000,
				synchronize: false,
				logging: false,
				// eslint-disable-next-line max-len
				entities: [
					ApiKey,
					BotRole,
					BotSocialConfig,
					Bot,
					FlagRole,
					FlagVariant,
					FeatureFlag,
					OrgDivision,
					Org,
					Policy,
					Resource,
					RolePolicy,
					Role,
					SsoConfig,
					StudioUser,
					Connection,
					UserAudit,
					UserRole,
					User,
				],
				subscribers: [],
				migrations: [],
				insecureAuth: false,
			});
		}

		return instance;
	}
}
