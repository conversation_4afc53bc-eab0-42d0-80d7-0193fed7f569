import { DataSource, EntityTarget, ObjectLiteral, Repository } from 'typeorm';
import { ConfigEnv } from '../init/config-env';
import { AppDataSource } from './app-data-source';

export class RepositoryWrapper<T extends ObjectLiteral> {
	protected readonly dataSource: DataSource;
	protected readonly repository: Repository<T>;
	protected readonly ce: ConfigEnv;

	constructor(target: EntityTarget<T>) {
		this.dataSource = AppDataSource.getInstance();
		this.repository = this.dataSource.getRepository(target);
		this.ce = ConfigEnv.getInstance();
	}
}
