import _ from 'lodash';
import { Injectable } from '@nestjs/common';
import { WebSocketGateway } from '../websocket/websocket.gateway';
import { RepositoryWrapper } from '../db/repository-wrapper';
import { FeatureFlag } from '../entity/flag.entity';
import { AuditMessageType, PassportUser, ValidationError } from '../auth/types';
import validator from 'validator';
import * as log from '../utils/log';
import { isEmpty } from 'lodash';
import { FlagRole } from '../entity/flag-role.entity';
import { AppErrors } from '../init/constants';
import { In, SelectQueryBuilder } from 'typeorm';
import { FlagVariant } from '../entity/flag-variant.entity';
import { KafkaService } from '../kafka/kafka.service';

@Injectable()
export class FlagService extends RepositoryWrapper<FeatureFlag> {
	constructor(
		private websocketGateway: WebSocketGateway,
		private kafkaService: KafkaService,
	) {
		super(FeatureFlag);
	}

	/**
	 * Returns the column name based on type of passed in value
	 * Number -> 'id'
	 * Else -> 'name'
	 * @param flagIdentitys
	 * @returns column name
	 */
	getColumnNameByFlagIdentity(flagIdentity: any): string {
		return typeof flagIdentity === 'number' || validator.isNumeric(`${flagIdentity}`) ? 'id' : 'name';
	}

	/**
	 * Check if Flag Exists by Id or Name
	 * @param author
	 * @param flagIdentity
	 * @returns
	 */
	async flagExists(author: PassportUser, flagIdentity: string): Promise<FeatureFlag | null> {
		log.enterx('FlagService', 'flagExists', author, `flagIdentity=${flagIdentity}`);
		const columnName = this.getColumnNameByFlagIdentity(flagIdentity);
		const criteria: any = {};
		criteria[columnName] = flagIdentity;
		const flag = await this.repository.findOneBy(criteria);

		log.exitx('FlagService', 'flagExists', author, `flagIdentity=${flagIdentity}, flagId=${flag?.id}, flagName=${flag?.name}`);
		return flag;
	}

	/**
	 * Check if flagRole exists by ID (PK of ff_roles)
	 * @param author
	 * @param id
	 * @returns
	 */
	async flagRoleExists(author: PassportUser, id: number): Promise<FlagRole | null> {
		log.enterx('FlagService', 'flagRoleExists', author, `flagRoleId=${id}`);
		const repo = this.dataSource.getRepository(FlagRole);
		const flagRole = await repo.findOneBy({ id });
		log.exitx('FlagService', 'flagRoleExists', author, `flagRoleId=${flagRole?.id}`);
		return flagRole;
	}

	/**
	 * Check if flag variant exists by PK=(id) or by UK=(flagId + varIndex)
	 * @param param
	 * @returns
	 */
	async flagVariantExists(param: { id?: number; flagId?: number; varIndex?: number }): Promise<FlagVariant | null> {
		const repo = this.dataSource.getRepository(FlagVariant);
		if (param.id) {
			return await repo.findOneBy({ id: param.id });
		} else if (param.flagId && param.varIndex) {
			return await repo.findOneBy({ flagId: param.flagId, varIndex: param.varIndex });
		}
		return null;
	}

	/**
	 * Find unique Flag Role by criteria
	 * @param author
	 * @param criteria
	 * @returns
	 */
	async findUniqueFlagRoleByCriteria(author: PassportUser, criteria: any): Promise<FlagRole | null> {
		log.enterx('FlagService', 'findUniqueFlagRoleByCriteria', author, `criteria=${JSON.stringify(criteria)}`);
		const repo = this.dataSource.getRepository(FlagRole);
		const flagRole = await repo.findOneBy(criteria);
		log.exitx('FlagService', 'findUniqueFlagRoleByCriteria', author, `flagRoleId=${flagRole?.id}`);
		return flagRole;
	}

	/**
	 * Find Flags and their defaultValue + offValue
	 * @param author
	 * @param options
	 * @returns
	 */
	async findFlags(author: PassportUser, options: any): Promise<FeatureFlag[]> {
		log.enterx('FlagService', 'findFlags', author);

		let whereClause = '1=1';

		if (options.active !== undefined) {
			whereClause += ` AND f.active = ${options.active}`;
		}
		// Add migrated filter if specified
		if (options.tags?.length) {
			const tagConditions = options.tags.map((tag: string) => `f.tags LIKE '%${tag}%'`).join(' OR ');
			whereClause += ` AND (${tagConditions})`;
		}

		const flags = await this.dataSource
			.getRepository(FeatureFlag)
			.createQueryBuilder('f')
			.leftJoin('f.defaultValue', 'fvd', 'f.id = fvd.flagId')
			.addSelect(['fvd.id', 'fvd.varIndex', 'fvd.name', 'fvd.value'])
			.leftJoin('f.offValue', 'fvo', 'f.id = fvo.flagId')
			.addSelect(['fvo.id', 'fvo.varIndex', 'fvo.name', 'fvo.value'])
			.where(whereClause)
			.getMany();

		// const flags = _.isEmpty(options) ? await this.repository.find() : await this.repository.findBy(options);
		log.exitx('FlagService', 'findFlags', author, `flags=${flags.length}`);
		return flags;
	}

	/**
	 * Find Paginated Flags and their defaultValue + offValue
	 * @param author
	 * @param options
	 * @returns
	 */
	async findPaginatedFlags(author: PassportUser, options: any): Promise<{ flags: FeatureFlag[]; totalFlagCount: number }> {
		log.enterx('FlagService', 'findPaginatedFlags', author, `pid=${process.pid}`);
		let whereClause = '1=1';
		if (options.tags?.length) {
			const tagConditions = options.tags.map((tag: string) => `f.tags LIKE '%${tag}%'`).join(' OR ');
			whereClause += ` AND (${tagConditions})`;
		}
		let query = this.dataSource
			.getRepository(FeatureFlag)
			.createQueryBuilder('f')
			.leftJoin('f.defaultValue', 'fvd', 'f.id = fvd.flagId')
			.addSelect(['fvd.id', 'fvd.varIndex', 'fvd.name', 'fvd.value'])
			.leftJoin('f.offValue', 'fvo', 'f.id = fvo.flagId')
			.addSelect(['fvo.id', 'fvo.varIndex', 'fvo.name', 'fvo.value'])
			.leftJoinAndSelect('f.flagRoles', 'fr')
			.leftJoinAndSelect('fr.onValue', 'fro', 'fr.flagId = fro.flagId') // <-- remove extra addSelect
			.leftJoin('fr.user', 'u')
			.addSelect(['u.id', 'u.email', 'u.uuid'])
			.leftJoin('fr.org', 'o')
			.addSelect(['o.id', 'o.name'])
			.leftJoin('fr.bot', 'b')
			.addSelect(['b.botId', 'b.name'])
			.leftJoin('fr.botRef', 'br')
			.addSelect(['br.id', 'br.env'])
			.where(whereClause)
			.andWhere('f.status not in ("Obsolete", "Archived", "Deprecated")')
			.orderBy('f.name', 'ASC');

		// Add filter for flagNames if provided
		if (Array.isArray(options?.flagNames) && options.flagNames.length) {
			query = query.andWhere('f.name IN (:...flagNames)', { flagNames: options.flagNames });
		}

		// Pagination
		query = query.take(options.limit || 100).skip(options.offset || 0)
		const totalFlagCount = await query.getCount(); 
		const flags = await query.getMany();
		log.exitx('FlagService', 'findPaginatedFlags', author, `flags=${flags.length}`, `pid=${process.pid}`);
		return { flags, totalFlagCount };
	}

	async findFlagsForEvaluation(author: PassportUser, options: any): Promise<FeatureFlag[]> {
		log.enterx('FlagService', 'findFlagsForEvaluation', author);
		let whereClause = '1=1';
		if (options.tags?.length) {
			const tagConditions = options.tags.map((tag: string) => `f.tags LIKE '%${tag}%'`).join(' OR ');
			whereClause += ` AND (${tagConditions})`;
		}
		let query = this.dataSource
			.getRepository(FeatureFlag)
			.createQueryBuilder('f')
			.leftJoin('f.defaultValue', 'fvd', 'f.id = fvd.flagId')
			.addSelect(['fvd.id', 'fvd.varIndex', 'fvd.name', 'fvd.value'])
			.leftJoin('f.offValue', 'fvo', 'f.id = fvo.flagId')
			.addSelect(['fvo.id', 'fvo.varIndex', 'fvo.name', 'fvo.value'])
			.leftJoinAndSelect('f.flagRoles', 'fr')
			.leftJoinAndSelect('fr.onValue', 'fro', 'fr.flagId = fro.flagId')
			.addSelect(['fro.id', 'fro.varIndex', 'fro.name', 'fro.value'])
			.leftJoin('fr.user', 'u')
			.addSelect(['u.id', 'u.email', 'u.uuid'])
			.leftJoin('fr.org', 'o')
			.addSelect(['o.id', 'o.name'])
			.leftJoin('fr.bot', 'b')
			.addSelect(['b.botId', 'b.name'])
			.leftJoin('fr.botRef', 'br')
			.addSelect(['br.id', 'br.env'])
			.where(whereClause)
			.andWhere('f.status not in ("Obsolete", "Archived", "Deprecated")')
			.orderBy('f.name', 'ASC');

		// Apply filters if needed
		if (options) {
			query = this.addCriteria(query, 'f.name', 'flagName', options);
			query = this.addCriteria(query, 'f.id', 'flagId', options);
		}

		const result = await query.getMany();
		log.exitx('FlagService', 'findFlagsForEvaluation', author, `result=${result.length}`);
		return result;
	}

	evaluateFlags(author: PassportUser, flags: FeatureFlag[], context: any): Record<string, any> {
		log.enterx('FlagService', 'evaluateFlags', author);
		const currentUserId = context.userId || null;
		const currentUserEmail = context.email || null;
		const currentUserOrgId = context.orgId || null;
		const currentServiceName = context.serviceName || null;
		const currentBotId = context.botId || null;
		const currentBotRefId = context.botRefId || null;
		const currentEnv = context.env || null;

		const result: Record<string, any> = {};
		const orderOfPrecedence = ['userEmail', 'userId', 'botId', 'botRefId', 'serviceName', 'env', 'orgId'];

		for (const flag of flags) {
			const flagName = flag.name;
			let value: any = null;
			const valueForAllRules: Record<string, any> = {};
			let matches = 0;

			// If flag is off
			if (flag.active === 0) {
				result[flagName] = flag.offValue?.value === 'true' ? true : flag.offValue?.value === 'false' ? false : (flag.offValue?.value ?? null);
				continue;
			}

			// Default value
			value = flag.defaultValue?.value ?? null;
			log.info('FlagService', 'evaluateFlags', `flagName=${flagName}, defaultValue=${value}`);

			// Role-based overrides
			for (const role of flag.flagRoles || []) {
				if (role.active === 0) continue;
				let onValue = role.onValue?.value;

				if (currentUserEmail && role.user?.email === currentUserEmail) {
					valueForAllRules['userEmail'] = onValue;
					matches++;
				}
				if (currentUserId && role.user?.id === currentUserId) {
					valueForAllRules['userId'] = onValue;
					matches++;
				}
				if (currentUserOrgId && role.orgId === currentUserOrgId) {
					valueForAllRules['orgId'] = onValue;
					matches++;
				}
				if (role.botRefId) {
					if ((currentBotId && currentBotId === role.botId) && (currentBotRefId && currentBotRefId === role.botRefId )) {
						valueForAllRules['botId'] = onValue;
						matches++;
					}
				}
				else if (currentBotId && currentBotId === role.botId) {
					valueForAllRules['botId'] = onValue;
					matches++;
				}
				if (currentBotRefId && role.botRefId === currentBotRefId) {
					valueForAllRules['botRefId'] = onValue;
					matches++;
				}
				if (currentServiceName && role.serviceName === currentServiceName) {
					valueForAllRules['serviceName'] = onValue;
					matches++;
				}
				if (currentEnv && role.env === currentEnv) {
					valueForAllRules['env'] = onValue;
					matches++;
				}
			}

			// Pick the value based on precedence
			if (matches) {
				for (const key of orderOfPrecedence) {
					if (valueForAllRules[key] !== undefined) {
						value = valueForAllRules[key];
						log.info('FlagService', 'evaluateFlags:match', `key=${key}`, `flagName=${flagName}, value=${value}`);
						break;
					}
				}
			}

			try {
				if (value?.indexOf('{') === 0 || value?.indexOf('[') === 0) {
					value = JSON.parse(value);
				}
			} catch (e) {}
			result[flagName] = value === 'true' ? true : value === 'false' ? false : value;
		}

		log.exitx('FlagService', 'evaluateFlags', author, `flagsCount=${Object.keys(result).length}`);
		return result;
	}

	/**
	 * Find flag by Id or Name
	 * @param author
	 * @param options
	 * @returns
	 */
	async findFlagByIdOrName(author: PassportUser, options: any): Promise<FeatureFlag | null> {
		log.enterx('FlagService', 'findFlagByIdOrName', author, JSON.stringify(options));
		const flag = await this.repository.findOneBy(options.id ? { id: options.id } : { name: options.name });
		log.exitx('FlagService', 'findFlagByIdOrName', author, `flag=${flag?.id}, name=${flag?.name}`);
		return flag;
	}

	addCriteria(fp0: SelectQueryBuilder<FeatureFlag>, column: string, key: string, options: any): SelectQueryBuilder<FeatureFlag> {
		if (options[key] && Array.isArray(options[key]) && options[key].length > 0) {
			fp0.andWhere(`${column} IN (:...${key})`, { [key]: options[key] });
		} else if (options[key]) {
			fp0.andWhere(`${column} = :${key}`, { [key]: options[key] });
		}
		return fp0;
	}

	/**
	 * Find flags and all their role and variant details
	 * @param author
	 * @param options - Provide options to filter flags
	 * @returns flags, their details, defualt, off and on values for the matching criteria
	 * Criteria is AND-ed only
	 */
	async findFlagProfile(author: PassportUser, options: any): Promise<FeatureFlag[] | null> {
		log.enterx('FlagService', 'findFlagProfile', author, `search: ${JSON.stringify(options)})`);
		let fp0 = this.dataSource
			.getRepository(FeatureFlag)
			.createQueryBuilder('f')
			.leftJoin('f.defaultValue', 'fvd', 'f.id = fvd.flagId')
			.addSelect(['fvd.id', 'fvd.varIndex', 'fvd.name', 'fvd.value'])
			.leftJoin('f.offValue', 'fvo', 'f.id = fvo.flagId')
			.addSelect(['fvo.id', 'fvo.varIndex', 'fvo.name', 'fvo.value'])
			.leftJoinAndSelect('f.flagRoles', 'fr')
			.leftJoinAndSelect('fr.onValue', 'fro', 'fr.flagId = fro.flagId')
			.addSelect(['fro.id', 'fro.varIndex', 'fro.name', 'fro.value'])
			.leftJoin('fr.user', 'u')
			.addSelect(['u.id', 'u.email', 'u.uuid'])
			.leftJoin('fr.org', 'o')
			.addSelect(['o.id', 'o.name'])
			.leftJoin('fr.bot', 'b')
			.addSelect(['b.botId', 'b.name', 'b.phase'])
			.leftJoin('fr.botRef', 'br')
			.addSelect(['br.name', 'br.env'])
			.where('1=1');

		// Add criteria based on the values provided
		fp0 = this.addCriteria(fp0, 'f.name', 'flagName', options);
		fp0 = this.addCriteria(fp0, 'f.active', 'flagActive', options);
		fp0 = this.addCriteria(fp0, 'f.status', 'status', options);
		fp0 = this.addCriteria(fp0, 'f.stage', 'stage', options);
		fp0 = this.addCriteria(fp0, 'f.id', 'flagId', options);
		fp0 = this.addCriteria(fp0, 'fr.id', 'flagRoleId', options);
		fp0 = this.addCriteria(fp0, 'fr.orgId', 'orgId', options);
		fp0 = this.addCriteria(fp0, 'fr.userId', 'userId', options);
		fp0 = this.addCriteria(fp0, 'fr.active', 'flagRoleActive', options);
		fp0 = this.addCriteria(fp0, 'u.email', 'email', options);
		fp0 = this.addCriteria(fp0, 'u.uuid', 'uuid', options);
		fp0 = this.addCriteria(fp0, 'fr.botId', 'botId', options);
		fp0 = this.addCriteria(fp0, 'fr.botRefId', 'botRefId', options);
		fp0 = this.addCriteria(fp0, 'fr.serviceName', 'serviceName', options);
		fp0 = this.addCriteria(fp0, 'fr.env', 'env', options);

		const result = await fp0.getMany();
		log.exitx('FlagService', 'findFlagProfile', author, `found flagProfiles=${result.length}`);
		return result;
	}

	/**
	 * Find all flag variants
	 * @param author
	 * @param options
	 * @returns
	 */
	async findFlagVariants(author: PassportUser, options: any): Promise<FlagVariant[] | []> {
		log.enterx('FlagService', 'findFlagVariants', author, `params=${JSON.stringify(options)}`);
		let flagId = 0;
		if (Number(options.flagIdentity)) {
			flagId = Number(options.flagIdentity);
		} else {
			const flag = await this.flagExists(author, options.flagIdentity);
			if (!flag) {
				log.exitx('FlagService', 'findFlagVariants', author, `Not Found=${options.flagIdentity}`);
				throw new ValidationError(AppErrors.FLAG_NOT_FOUND);
			}
			flagId = flag.id;
		}
		const repo = this.dataSource.getRepository(FlagVariant);
		const flagVariants = await repo.findBy({ flagId });
		log.exitx('FlagService', 'findFlagVariants', author, `flagVariants=${flagVariants.length}`);
		return flagVariants;
	}

	/**
	 * saveFlagVariant
	 * @param author
	 * @param body { id: PrimaryKey or (flagId, varIndex)}
	 * @returns
	 */
	async saveFlagVariant(author: PassportUser, body: any): Promise<FeatureFlag[] | null> {
		if (!body) return null;
		log.enterx('FlagService', 'saveFlagVariant', author, `id=${body.id}, flagId=${body.flagId},varIndex=${body.varIndex}`);
		let f0: FlagVariant | null = await this.flagVariantExists(body);
		let auditAction = 'update-flag-variant';
		if (!f0) {
			f0 = new FlagVariant();
			auditAction = 'create-flag-variant';
		}
		f0.id = body.id;
		f0.flagId = body.flagId;
		f0.varIndex = body.varIndex;
		f0.name = body.name;
		f0.value = body.value;

		const repo1 = this.dataSource.getRepository(FlagVariant);
		const fv1 = await repo1.save(f0);
		const fp0 = await this.findFlagProfile(author, { flagId: fv1.flagId });

		// Save in audit
		if (fp0 && fp0.length > 0) {
			try {
				const auditMessage: AuditMessageType = {
					action: auditAction,
					flagName: fp0[0].name,
					loginIp: body.headers?.loginIp,
					loginAgent: body.headers?.loginAgent,
					notes: {
						id: fv1.id,
						flagId: fp0[0].id,
						variant: {
							varIndex: fv1.varIndex,
							name: fv1.name,
							value: fv1.value,
						},
						active: fv1.active,
						version: fp0[0].version,
					},
				};
				await this.sendAuditMessage(author, auditMessage);
			} catch (e) {
				log.warnx('FlagService', 'saveFlag', author, 'failed to save audit');
			}
		}

		log.exitx('FlagService', 'saveFlagVariant', author, `flagName=${body?.name}`);
		return fp0;
	}

	async saveFlag(author: PassportUser, body: any): Promise<FeatureFlag | null> {
		log.enterx('FlagService', 'saveFlag', author, `flagName=${body.name}`);
		let f0: FeatureFlag | null = await this.flagExists(author, body.id || body.name);
		let auditAction = 'update-flag';
		// make a copy to identify diffs in audit notes
		const f00 = f0 ? JSON.parse(JSON.stringify(f0)) : {};
		if (!f0) {
			f0 = new FeatureFlag();
			auditAction = 'create-flag';
		}
		f0.name = body.name?.trim();
		f0.description = body.description?.trim();
		f0.type = body.type;
		f0.status = body.status;
		f0.stage = body.stage;
		f0.version = body.version || (f0.version ? Number(f0.version) + 1 : 1);
		f0.tags = body.tags;
		f0.settings = body.settings;
		f0.active = body.active;
		f0.defaultVariant = body.defaultVariant;
		f0.offVariant = body.offVariant;

		if (isEmpty(f0.id)) {
			f0.createdBy = author.id;
		}
		f0.updatedBy = author.id;
		const f1 = await this.repository.save(f0);
		// Save in audit
		try {
			const auditMessage: AuditMessageType = {
				action: auditAction,
				flagName: f1.name,
				loginIp: body.headers?.loginIp,
				loginAgent: body.headers?.loginAgent,
				notes: {
					version: f1.version,
					oldDefaultVariant: f00.defaultVariant,
					oldOffVariant: f00.offVariant,
					newDefaultVariant: f1.defaultVariant,
					newOffVariant: f1.offVariant,
					status: f1.status === f00.status ? undefined : f1.status,
					stage: f1.stage === f00.stage ? undefined : f1.stage,
					tags: f1.tags === f00.tags ? undefined : f1.tags,
					active: f1.active === f00.active ? undefined : f1.active,
				},
			};
			await this.sendAuditMessage(author, auditMessage);
		} catch (e) {
			log.warnx('FlagService', 'saveFlag', author, 'failed to save audit');
		}

		log.exitx('FlagService', 'saveFlag', author, `flagName=${f1?.name}, flagId=${f1?.id}`);
		return f1;
	}

	async saveFlagRole(author: PassportUser, body: any): Promise<FeatureFlag[] | null> {
		const { flagId, flagRoleId, flagName } = body;
		let f0 = await this.findFlagByIdOrName(author, { id: flagId, name: flagName });
		if (!f0) {
			throw new ValidationError(AppErrors.FLAG_NOT_FOUND);
		}
		log.enterx('FlagService', 'saveFlagRole', author, `flagRoleId=${flagRoleId}; flagId=${f0.id}; name=${f0.name}`);
		let fr0 = null;
		let auditAction = 'update-flag-role';
		if (flagRoleId) {
			fr0 = await this.findUniqueFlagRoleByCriteria(author, { id: flagRoleId });
		}
		if (!fr0) {
			fr0 = new FlagRole();
			auditAction = 'create-flag-role';
		}
		fr0.flagId = f0.id;
		fr0.userId = body.userId;
		fr0.orgId = body.orgId;
		fr0.orgDivisionId = body.orgDivisionId;
		fr0.env = body.env;
		fr0.botRefId = body.botRefId;
		fr0.botId = body.botId;
		fr0.serviceName = body.serviceName;
		fr0.env = body.env;
		fr0.onVariant = 'onVariant' in body ? body.onVariant : 'onValue' in body ? body.onValue : 0;
		fr0.negate = body.negate;
		fr0.settings = body.settings;
		fr0.status = body.status;
		fr0.active = body.active;

		if (isEmpty(fr0.id)) {
			fr0.createdBy = author.id;
		}

		fr0.updatedBy = author.id;
		log.jsonx('FlagService', 'saveFlagRole', author, 'FlagRole', fr0);
		const repo1 = this.dataSource.getRepository(FlagRole);
		const fr1 = await repo1.save(fr0);
		const fp0 = await this.findFlagProfile(author, { flagId: fr1.flagId });

		// Notify subscribers
		if (fp0 && fp0.length > 0) {
			this.websocketGateway.updateFlag(fp0[0].name, fp0[0].flagRoles[0].onVariant);
		}

		// Save in audit
		try {
			const auditMessage: AuditMessageType = {
				action: auditAction,
				flagName: f0.name,
				userId: fr1.userId,
				botId: fr1.botId,
				botRefId: fr1.botRefId,
				orgId: fr1.orgId,
				orgDivisionId: fr1.orgDivisionId,
				loginIp: body.headers?.loginIp,
				loginAgent: body.headers?.loginAgent,
				notes: {
					onVariant: fr1.onVariant,
					active: fr1.active,
					serviceName: fr1.serviceName,
					env: fr1.env,
					negate: fr1.negate,
					version: fr0.version,
				},
			};
			await this.sendAuditMessage(author, auditMessage);
		} catch (e) {
			log.warnx('FlagService', 'saveFlagRole', author, 'failed to save audit');
		}

		log.exitx('FlagService', 'saveFlagRole', author, `flagId=${f0.id}, flagName=${f0.name}, flagRoleId=${fr1.id}`);
		return fp0;
	}

	async sendAuditMessage(author: PassportUser, message: AuditMessageType) {
		if (!author) return;
		if (!message) return;
		const auditMsg = {
			traceId: author.traceId,
			flagName: message.flagName,
			action: message.action,
			userId: author.id,
			email: author.email,
			uuid: author.uuid,
			orgId: author.orgId,
			botId: message.botId,
			botRefId: message.botRefId,
			notes: message.notes,
		};
		await this.kafkaService.sendAuditMessage(auditMsg);
	}
	async saveImportedFlags(author: PassportUser, flags: any[]): Promise<{ success: FeatureFlag[]; error?: string }> {
		const existingFlags = [];
		for (const flag of flags) {
			const exists = await this.flagExists(author, flag.name);
			if (exists) {
				existingFlags.push(flag.name);
			}
		}
		if (existingFlags.length > 0) {
			return {
				success: [],
				error: `One or more flags already exist: ${existingFlags.join(', ')}`,
			};
		}
		// All flags are new, proceed to save
		const success: FeatureFlag[] = [];

		for (const flag of flags) {
			try {
				const saved = await this.saveImportedFlag(author, flag);
				if (saved) {
					success.push(saved);
				}
			} catch (err) {
				log.warnx('FlagService', 'saveImportedFlags', author, `Failed to import flag ${flag.name}: ${err.message}`);
				throw new Error(`Failed to import flag ${flag.name}: ${err.message}`);
			}
		}

		return { success };
	}

	async saveImportedFlag(author: PassportUser, body: any): Promise<FeatureFlag | null> {
		log.enterx('FlagService', 'saveImportedFlag', author, `flagName=${body.name}`);
		let auditAction = 'import-flag';
		let f0 = new FeatureFlag();
		f0.name = body.name?.trim();
		f0.description = body.description?.trim();
		f0.type = body.type;
		f0.status = body.status;
		f0.stage = body.stage;
		f0.version = body.version || (f0.version ? Number(f0.version) + 1 : 1);
		f0.tags = body.tags;
		f0.settings = body.settings;
		f0.active = body.active;
		f0.defaultVariant = body.defaultVariant;
		f0.offVariant = body.offVariant;

		if (isEmpty(f0.id)) {
			f0.createdBy = author.id;
		}
		f0.updatedBy = author.id;
		const f1 = await this.repository.save(f0);
		const variants = [
			{ ...body.defaultValue, flagId: f1.id },
			{ ...body.offValue, flagId: f1.id },
		];

		for (const variant of variants) {
			await this.saveFlagVariant(author, variant);
		}
		// Save in audit
		try {
			const auditMessage: AuditMessageType = {
				action: auditAction,
				flagName: f1.name,
				loginIp: body.headers?.loginIp,
				loginAgent: body.headers?.loginAgent,
				notes: {
					version: f1.version,
					newDefaultVariant: f1.defaultVariant,
					newOffVariant: f1.offVariant,
					status: f1.status,
					stage: f1.stage,
					tags: f1.tags,
					active: f1.active,
				},
			};
			await this.sendAuditMessage(author, auditMessage);
		} catch (e) {
			log.warnx('FlagService', 'saveImportedFlag', author, 'failed to save audit');
		}

		log.exitx('FlagService', 'saveImportedFlag', author, `flagName=${f1?.name}, flagId=${f1?.id}`);
		return f1;
	}
	async deleteFlags(author: PassportUser, flagNames: string[]): Promise<{ success: string; deletedFlags: string[] }> {
		log.enterx('FlagService', 'deleteFlags', author, `flags=${flagNames.join(', ')}`, `pid=${process.pid}`);

		// Validate input
		if (!flagNames || flagNames.length === 0) {
			throw new ValidationError('flagNames array cannot be empty');
		}

		// Find flags that exist
		const existingFlags = await this.repository.find({
			where: { name: In(flagNames) },
			select: ['id', 'name']
		});

		if (existingFlags.length === 0) {
			throw new ValidationError(AppErrors.FLAG_NOT_FOUND);
		}

		const existingFlagNames = existingFlags.map(flag => flag.name);
		const notFoundFlags = flagNames.filter(name => !existingFlagNames.includes(name));

		if (notFoundFlags.length > 0) {
			throw new ValidationError(`Flags not found: ${notFoundFlags.join(', ')}`);
		}

		const flagIds = existingFlags.map(flag => flag.id);
		const deletedFlagNames: string[] = [];

		// Use transaction to ensure data consistency
		await this.dataSource.transaction(async manager => {
			// Step 1: Delete flag roles first (child table)
			const flagRoleRepo = manager.getRepository(FlagRole);
			const deletedRoles = await flagRoleRepo.delete({ flagId: In(flagIds) });
			log.infox('FlagService', 'deleteFlags', author, `Deleted ${deletedRoles.affected} flag roles`);

			// Step 2: Clear foreign key references in feature_flags to break circular dependency
			await manager.query(
				`UPDATE feature_flags SET default_variant = NULL, off_variant = NULL WHERE id IN (${flagIds.join(',')})`
			);
			log.infox('FlagService', 'deleteFlags', author, `Cleared variant references for ${flagIds.length} flags`);

			// Step 3: Delete flag variants (now safe to delete)
			const flagVariantRepo = manager.getRepository(FlagVariant);
			const deletedVariants = await flagVariantRepo.delete({ flagId: In(flagIds) });
			log.infox('FlagService', 'deleteFlags', author, `Deleted ${deletedVariants.affected} flag variants`);

			// Step 4: Delete feature flags (parent table)
			const flagRepo = manager.getRepository(FeatureFlag);
			const deletedFlags = await flagRepo.delete({ id: In(flagIds) });
			log.infox('FlagService', 'deleteFlags', author, `Deleted ${deletedFlags.affected} feature flags`);

			// Track successfully deleted flags
			deletedFlagNames.push(...existingFlagNames);

			// Send audit messages for each deleted flag
			for (const flag of existingFlags) {
				try {
					const auditMessage: AuditMessageType = {
						action: 'delete-flag',
						flagName: flag.name,
						notes: {
							flagId: flag.id,
							deletedAt: new Date().toISOString(),
						},
					};
					await this.sendAuditMessage(author, auditMessage);
				} catch (e) {
					log.warnx('FlagService', 'deleteFlags', author, `Failed to send audit message for flag ${flag.name}`);
				}
			}
		});

		const result = {
			success: `Successfully deleted ${deletedFlagNames.length} flags and their associated roles and variants`,
			deletedFlags: deletedFlagNames
		};

		log.exitx('FlagService', 'deleteFlags', author, `flags=${flagNames.join(', ')}`, `deleted=${deletedFlagNames.length}`, `pid=${process.pid}`);
		return result;
	}


	
}
