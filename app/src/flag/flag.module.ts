import { Modu<PERSON> } from '@nestjs/common';
import { FlagService } from './flag.service';
import { FlagController } from './flag.controller';
import { WebSocketModule } from '../websocket/websocket.module';
import { ConnectionService } from './connection.service';
import { KafkaService } from '../kafka/kafka.service';
@Module({
	controllers: [FlagController],
	providers: [FlagService, KafkaService, ConnectionService],
	imports: [WebSocketModule],
})
export class FlagModule {}
