import { Injectable } from '@nestjs/common';
import { RepositoryWrapper } from '../db/repository-wrapper';
import { Connection } from '../entity/connection.entity';
import * as log from '../utils/log';
import { ValidationError } from '../auth/types';

@Injectable()
export class ConnectionService extends RepositoryWrapper<Connection> {
	constructor() {
		super(Connection);
	}

	async connectionExists(clientId: string): Promise<Connection | null> {
		log.enter('ConnectionService', `connectionExists(${clientId})`);
		let c0 = await this.repository.findOneBy({ clientId });
		log.exit('ConnectionService', `connectionExists(${clientId})`, `connections=${c0?.id || 'None'}`);
		return c0;
	}

	async findConnections(options: any): Promise<Connection[]> {
		log.enter('ConnectionService', 'findConnections', JSON.stringify(options));
		const connections = await this.repository.findBy(options);
		log.exit('ConnectionService', 'findConnections', `connection=${connections.length}`);
		return connections;
	}

	async findActiveConnections(): Promise<Connection[]> {
		log.enter('ConnectionService', 'findActiveConnections');
		const connections = await this.repository.findBy({ status: 'connect', active: 1 });
		log.exit('ConnectionService', 'findActiveConnections', `connection=${connections.length}`);
		return connections;
	}

	async findConnectionByClientId(clientId: string): Promise<Connection | null> {
		log.enter('ConnectionService', `findConnectionByClientId(${clientId})`);
		let c0 = await this.repository.findOneBy({ clientId });
		log.exit('ConnectionService', `findConnectionByClientId(${clientId})`, `connections=${c0?.id || 'None'}`);
		return c0;
	}

	async findFlagsByClientId(clientId: string): Promise<string> {
		log.enter('ConnectionService', `findFlagsByClientId(${clientId})`);
		let c0 = await this.repository.findOneBy({ clientId });
		if (c0) {
			log.exit('ConnectionService', `findFlagsByClientId(${clientId})`, `flags=${c0.flags}`);
			return c0.flags;
		}
		log.exit('ConnectionService', `findFlagsByClientId(${clientId})`, `Failed to find flags`);
		return '';
	}

	async saveConnection(body: any): Promise<Connection | null> {
		if (!body.clientId) {
			throw new ValidationError('Missing clientId', 'ClientId is required to save a connection');
		}
		log.enter('ConnectionService', 'saveConnection', `clientId=${body.clientId}`);
		let c0: Connection | null = await this.findConnectionByClientId(body.clientId);
		if (!c0) {
			c0 = new Connection();
		}
		c0.clientId = body.clientId;
		c0.clientName = body.clientName;
		c0.clientIp = body.clientIp;
		c0.clientHost = body.clientHost;
		c0.flags = this.mergeUniqueCSV(c0.flags, body.flags);
		c0.status = body.status;
		c0.callbackType = body.callbackType;
		c0.settings = body.settings;
		c0.notes = body.notes;
		c0.active = body.active;
		c0.issuedAt = body.issuedAt;

		const c1 = await this.repository.save(c0);
		log.exit('ConnectionService', 'saveConnection', `clientId=${c1?.clientId}`);
		return c1;
	}

	mergeUniqueCSV(p0: string | null, p1: string | null): string {
		// Convert strings to sets to ensure uniqueness
		const set = new Set<string>();

		if (p0) {
			if (Array.isArray(p0)) {
				p0.forEach((x) => set.add(x.trim()));
			} else if (typeof p0 === 'string') {
				p0.split(',').forEach((x) => set.add(x.trim()));
			}
			p0.split(',').forEach((x) => set.add(x.trim()));
		}

		if (p1) {
			if (Array.isArray(p1)) {
				p1.forEach((x) => set.add(x.trim()));
			} else if (typeof p1 === 'string') {
				p1.split(',').forEach((x) => set.add(x.trim()));
			}
		}

		// Convert the set back to a comma-separated string
		return Array.from(set).join(',');
	}

	// async notifySubscribers(fp0: Connection) {
	// 	const repo2 = this.dataSource.getRepository(Subscription);
	// 	const subscriptions = await repo2.find({
	// 		where: { ConnectionName: fp0.name },
	// 	});
	// 	await Promise.all(
	// 		subscriptions.map(async (sub) => {
	// 			if (sub.callbackType === 'REST') {
	// 				// await axios.post(sub.callbackUrlOrSocketId, { ConnectionName: fp0.name, value });
	// 			} else if (sub.callbackType === 'Webhook') {
	// 				// await axios.post(sub.callbackUrlOrSocketId, { ConnectionName: fp0.name, value });
	// 			} else if (sub.callbackType === 'WebSocket') {
	// 				this.websocketGateway.sendMessage(sub.callbackUrlOrSocketId, {});
	// 			}
	// 		}),
	// 	);
	// }
}
