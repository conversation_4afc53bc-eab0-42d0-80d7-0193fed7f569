import { Controller, Post, Get, Req, Put } from '@nestjs/common';
import { FlagService } from './flag.service';
import { ApiOperation, ApiResponse, ApiTags, ApiParam } from '@nestjs/swagger';
import { Api, DefaultErrorsResponse, failureResponse, missingCredentialsResponse, missingRequiredParamResponse, notFoundResponse, ResponsePayloadType, successResponse,successResponseForPaginatedFlags } from '../utils/api';
import { FeatureFlag } from '../entity/flag.entity';
import * as log from '../utils/log';
import { AppErrors } from '../init/constants';
import { Request } from 'express';
import { FlagVariant } from '../entity/flag-variant.entity';


const MAX_FLAGS_RESPONSE_SIZE = 500;
const MAX_FLAGS_COUNT = 100;
@Controller()
@ApiTags('feature-flags')
export class FlagController {
	constructor(private readonly flagService: FlagService) {}

	@ApiOperation({ summary: Api.getFlags.purpose })
	@ApiResponse({ status: 200, description: Api.getFlags.successMessage, type: ResponsePayloadType<FeatureFlag[]> })
	@DefaultErrorsResponse()
	@Get(Api.getFlags.url)
	async getFlags(@Req() req: Request): Promise<ResponsePayloadType<FeatureFlag[]>> {
		const author = req.passportUser;
		const allFlags = (req.query['allFlags'] as string)?.toLowerCase() === 'true';
		const options = allFlags ? {} : { active: 1 };
		if (!author) {
			return missingCredentialsResponse(req, Api.getFlags);
		}
		const flags = await this.flagService.findFlags(author, options);
		return successResponse(req, Api.getFlags, flags);
	}

	@ApiOperation({ summary: Api.getPaginatedFlagsWithRoles.purpose })
	@ApiResponse({ status: 200, description: Api.getPaginatedFlagsWithRoles.successMessage, type: ResponsePayloadType<FeatureFlag[]> })
	@DefaultErrorsResponse()
	@Get(Api.getPaginatedFlagsWithRoles.url)
	async getPaginatedFlagsWithRoles(@Req() req: Request): Promise<ResponsePayloadType<FeatureFlag[]>> {
		const author = req.passportUser;
		log.enterx('FlagService', 'getPaginatedFlagsWithRoles', author, `pid=${process.pid}`);
		const allFlags = (req.query['allFlags'] as string)?.toLowerCase() === 'true';
		if (!author) {
			return missingCredentialsResponse(req, Api.getPaginatedFlagsWithRoles);
		}
		const offset = parseInt(req.query['offset'] as string) || 0;
		const options: { active?: number; limit: number; offset: number } = {
		...(allFlags ? {} : { active: 1 }),
			limit: MAX_FLAGS_COUNT,
			offset
		};

		const { flags: rawFlags, totalFlagCount }  = await this.flagService.findPaginatedFlags(author, options);

		// Enforce max response size
		const selectedFlags: FeatureFlag[] = [];
		let accumulatedSize = 0;

		for (const flag of rawFlags) {
			const flagStr = JSON.stringify(flag);
			const flagSize = Buffer.byteLength(flagStr, 'utf8') / 1024; // in KB

			if (accumulatedSize + flagSize > MAX_FLAGS_RESPONSE_SIZE) {
				break;
			}

			selectedFlags.push(flag);
			accumulatedSize += flagSize;
		}

		const lastReturnedIndex = offset + selectedFlags.length; 
		log.exitx('FlagService', 'getPaginatedFlagsWithRoles', author, `pid=${process.pid}`);
		return successResponseForPaginatedFlags(req, Api.getPaginatedFlagsWithRoles, selectedFlags, { lastReturnedIndex, totalFlagCount });
	}

	@ApiOperation({ summary: Api.postPaginatedFlagsWithRoles.purpose })
	@ApiResponse({ status: 200, description: Api.postPaginatedFlagsWithRoles.successMessage, type: ResponsePayloadType<FeatureFlag[]> })
	@DefaultErrorsResponse()
	@Post(Api.postPaginatedFlagsWithRoles.url)
	async postPaginatedFlagsWithRoles(@Req() req: Request): Promise<ResponsePayloadType<FeatureFlag[]>> {
		const author = req.passportUser;
		log.enterx('FlagService', 'postPaginatedFlagsWithRoles', author, `pid=${process.pid}`);
		const allFlags = (req.query['allFlags'] as string)?.toLowerCase() === 'true';
		if (!author) {
			return missingCredentialsResponse(req, Api.postPaginatedFlagsWithRoles);
		}
		const offset = parseInt(req.query['offset'] as string) || 0;
		const flagNames = req.body?.flagNames as string[] || [];
		const options: { active?: number; limit: number; offset: number; flagNames: string[] } = {
		...(allFlags ? {} : { active: 1 }),
			limit: MAX_FLAGS_COUNT,
			offset,
			flagNames
		};

		const { flags: rawFlags, totalFlagCount }  = await this.flagService.findPaginatedFlags(author, options);

		// Enforce max response size
		const selectedFlags: FeatureFlag[] = [];
		let accumulatedSize = 0;

		for (const flag of rawFlags) {
			const flagStr = JSON.stringify(flag);
			const flagSize = Buffer.byteLength(flagStr, 'utf8') / 1024; // in KB

			if (accumulatedSize + flagSize > MAX_FLAGS_RESPONSE_SIZE) {
				break;
			}

			selectedFlags.push(flag);
			accumulatedSize += flagSize;
		}

		const lastReturnedIndex = offset + selectedFlags.length; 
		log.exitx('FlagService', 'postPaginatedFlagsWithRoles', author, `pid=${process.pid}`);
		return successResponseForPaginatedFlags(req, Api.postPaginatedFlagsWithRoles, selectedFlags, { lastReturnedIndex, totalFlagCount });
	}

	@ApiOperation({ summary: Api.getFlagVariants.purpose })
	@ApiResponse({ status: 200, description: Api.getFlagVariants.successMessage, type: ResponsePayloadType<FeatureFlag[]> })
	@DefaultErrorsResponse()
	@Get(Api.getFlagVariants.url)
	async getFlagVariants(@Req() req: Request): Promise<ResponsePayloadType<FlagVariant[]>> {
		const author = req.passportUser;
		if (!author) {
			return missingCredentialsResponse(req, Api.postFlagProfile);
		}
		if (!req.params['flagIdentity']) {
			return missingRequiredParamResponse(req, Api.getFlagVariants, 'Flag Id or Flag Name is required');
		}
		const flagVariants = await this.flagService.findFlagVariants(author, { flagIdentity: req.params['flagIdentity'] });
		return successResponse(req, Api.getFlagVariants, flagVariants);
	}

	@ApiOperation({ summary: Api.postFlagProfile.purpose })
	@ApiResponse({ status: 200, description: Api.postFlagProfile.successMessage, type: ResponsePayloadType<FeatureFlag[]> })
	@ApiParam({ name: 'flag', required: true, description: 'Flag Id or Flag Name', type: String })
	@DefaultErrorsResponse()
	@Post(Api.postFlagProfile.url)
	async postFlagProfile(@Req() req: Request): Promise<ResponsePayloadType<FeatureFlag[]>> {
		const body = req.body;
		const allFlags = (req.query['allFlags'] as string)?.toLowerCase() === 'true';

		try {
			const author = req.passportUser;
			if (!author) {
				return missingCredentialsResponse(req, Api.postFlagProfile);
			}
			// if (!body.flagId && !body.flagRoleId && !body.flagName && !body.botId && !body.botRefId && !body.orgId && !body.serviceName && !body.env && !body.userId && !body.uuid && !body.email) {
			// 	return missingRequiredParamResponse(
			// 		req,
			// 		Api.postFlagProfile,
			// 		'At least one filter parameter is required (flagId, flagName, flagRoleId, botId, botRefId, orgId, serviceName, env, userId, uuid, email)',
			// 	);
			// }

			const flagsWithRoles = (await this.flagService.findFlagProfile(author, body)) || [];
			if (allFlags) {
				let activeOption: any = {
					active: 'flagActive' in body ? body.flagActive : 'active' in body ? body.active : undefined,
					tags: body?.tags?.length ? body.tags : undefined,
				};
				const flags = (await this.flagService.findFlags(author, activeOption)) || [];
				for (const f0 of flagsWithRoles) {
					const f1 = flags.find((x) => x.name === f0.name);
					if (f1) {
						f1.flagRoles = f0.flagRoles;
					}
				}
				if (flags) {
					log.infox('FlagController', 'getFlagProfile', author, flags.length);
					return successResponse(req, Api.postFlagProfile, flags);
				}
			} else {
				return successResponse(req, Api.postFlagProfile, flagsWithRoles);
			}
			return notFoundResponse(req, Api.postFlagProfile, AppErrors.FLAG_NOT_FOUND, {}, ['Flag may be Archived']);
		} catch (e) {
			return failureResponse(req, Api.postFlagProfile, e.message);
		}
	}

	@ApiOperation({ summary: Api.evaluateFlag.purpose })
	@ApiResponse({
		status: 200,
		description: Api.evaluateFlag.successMessage,
		type: ResponsePayloadType<FeatureFlag[]>,
	})
	@DefaultErrorsResponse()
	@Post(Api.evaluateFlag.url)
	async evaluateFlag(@Req() req: Request): Promise<ResponsePayloadType<FeatureFlag[]>> {
		const body = req.body;
		try {
			const author = req.passportUser;
			if (!author) {
				return missingCredentialsResponse(req, Api.evaluateFlag);
			}
			const flags = await this.flagService.findFlagsForEvaluation(author, body);

			if (flags?.length) {
				const formattedFlags = this.flagService.evaluateFlags(author, flags, {
					userId: body?.userId,
					email: body?.email,
					orgId: body?.orgId,
					botId: body?.botId,
					botRefId: body?.botRefId,
					serviceName: body?.serviceName,
				});

				return successResponse(req, Api.postFlagProfile, formattedFlags);
			}

			return notFoundResponse(req, Api.evaluateFlag, AppErrors.FLAG_NOT_FOUND, {}, ['Flag may be Archived']);
		} catch (e) {
			return failureResponse(req, Api.evaluateFlag, e.message);
		}
	}

	@ApiOperation({ summary: Api.putFlag.purpose })
	@ApiResponse({ status: 200, description: Api.putFlag.successMessage, type: ResponsePayloadType<FeatureFlag> })
	@DefaultErrorsResponse()
	@Put(Api.putFlag.url)
	async putFlag(@Req() req: Request): Promise<ResponsePayloadType<FeatureFlag>> {
		try {
			const author = req.passportUser;
			if (!author) {
				return missingCredentialsResponse(req, Api.postFlagProfile);
			}
			const body = req.body;
			const flag = await this.flagService.saveFlag(author, body);
			if (flag) {
				log.infox('FlagController', 'putFlag', author, flag.name);
				return successResponse(req, Api.putFlag, flag);
			}
		} catch (e) {
			return failureResponse(req, Api.putFlag, e.message);
		}
		return failureResponse(req, Api.putFlag, 'Attempted to save flag, but no response received');
	}

	@ApiOperation({ summary: Api.putFlagVariant.purpose })
	@ApiResponse({ status: 200, description: Api.putFlag.successMessage, type: ResponsePayloadType<FeatureFlag> })
	@DefaultErrorsResponse()
	@Put(Api.putFlagVariant.url)
	async putFlagVariant(@Req() req: Request): Promise<ResponsePayloadType<FeatureFlag>> {
		try {
			const author = req.passportUser;
			if (!author) {
				return missingCredentialsResponse(req, Api.putFlagRole);
			}
			const body = req.body;
			if (!body.flagId) {
				return missingRequiredParamResponse(req, Api.putFlagVariant, 'flagId');
			}
			if (!body.name) {
				return missingRequiredParamResponse(req, Api.putFlagVariant, 'name');
			}
			if (body.value === null || body.value === undefined) {
				return missingRequiredParamResponse(req, Api.putFlagVariant, 'value');
			}
			if (body.varIndex === null || body.varIndex === undefined) {
				return missingRequiredParamResponse(req, Api.putFlagVariant, 'varIndex');
			}
			const flags = await this.flagService.saveFlagVariant(author, body);
			if (flags) {
				log.infox('FlagController', 'putFlagRole', author, flags.length);
				return successResponse(req, Api.putFlagVariant, flags);
			}
		} catch (e) {
			return failureResponse(req, Api.putFlagVariant, e.message);
		}
		return failureResponse(req, Api.putFlagVariant, `Attempted to save the flag variant flagId=${req.body?.flagId}/varIndex=${req.body?.varIndex}, but no response received`);
	}

	@ApiOperation({ summary: Api.putFlag.purpose })
	@ApiResponse({ status: 200, description: Api.putFlag.successMessage, type: ResponsePayloadType<FeatureFlag> })
	@DefaultErrorsResponse()
	@Put(Api.putFlagRole.url)
	async putFlagRole(@Req() req: Request): Promise<ResponsePayloadType<FeatureFlag>> {
		try {
			const author = req.passportUser;
			if (!author) {
				return missingCredentialsResponse(req, Api.putFlagRole);
			}
			const body = req.body;
			const flags = await this.flagService.saveFlagRole(author, body);
			if (flags) {
				log.infox('FlagController', 'putFlagRole', author, flags.length);
				return successResponse(req, Api.putFlagRole, flags);
			}
		} catch (e) {
			return failureResponse(req, Api.putFlagRole, e.message);
		}
		return failureResponse(req, Api.putFlagRole, 'Attempted to save flag role, but no response received');
	}

	@ApiOperation({ summary: Api.importFlags.purpose })
	@ApiResponse({ status: 200, description: Api.importFlags.successMessage, type: ResponsePayloadType<FeatureFlag[]> })
	@DefaultErrorsResponse()
	@Post(Api.importFlags.url)
	async importFlags(@Req() req: Request): Promise<ResponsePayloadType<FeatureFlag[]>> {
		try {
			const author = req.passportUser;
			if (!author) {
				return missingCredentialsResponse(req, Api.importFlags);
			}
			const body = req.body;
			if (!Array.isArray(body)) {
				return failureResponse(req, Api.importFlags, 'Invalid payload. Expected array of flags.');
			}
			const results = await this.flagService.saveImportedFlags(author, body);

			if (results.error) {
				return failureResponse(req, Api.importFlags, results.error);
			}

			const importedFlags = results.success;

			log.infox('FlagController', 'importFlags', author, `Imported: ${importedFlags.length}`);
			return successResponse(req, Api.importFlags, importedFlags);
		} catch (e) {
			return failureResponse(req, Api.importFlags, e.message);
		}
	}


	@ApiOperation({ summary: Api.deleteFlags.purpose })
	@ApiResponse({ status: 200, description: Api.deleteFlags.successMessage, type: ResponsePayloadType<FeatureFlag[]> })
	@DefaultErrorsResponse()
	@Post(Api.deleteFlags.url)
	async deleteFlags(@Req() req: Request): Promise<ResponsePayloadType<FeatureFlag[]>> {
		try {
			const author = req.passportUser;
			if (!author) {
				return missingCredentialsResponse(req, Api.deleteFlags);
			}
			const flagNames = req.body?.flagNames as string[] || [];
			if (!Array.isArray(flagNames) || flagNames.length === 0) {
				return failureResponse(req, Api.deleteFlags, 'flagNames must be a non-empty array.');
			}
			const results = await this.flagService.deleteFlags(author, flagNames);

			if (results.error) {
				return failureResponse(req, Api.deleteFlags, results.error);
			}

			const deletedFlags = results.success;

			log.infox('FlagController', 'deleteFlags', author, `message: ${deletedFlags}`);
			return successResponse(req, Api.deleteFlags, deletedFlags);
		} catch (e) {
			return failureResponse(req, Api.deleteFlags, e.message);
		}
	}

	// @Post('subscribe')
	// subscribeToFlag(@Body() body: { flagName: string; callbackType: 'REST' | 'WebSocket' | 'Webhook'; callbackUrlOrSocketId: string }) {
	// 	return this.connectionService.subscribe(body.flagName, body.callbackType, body.callbackUrlOrSocketId);
	// }

	// @ApiOperation({ summary: Api.evaluateFlag.purpose })
	// @ApiResponse({ status: 200, description: Api.evaluateFlag.successMessage, type: ResponsePayloadType<FeatureFlag[]> })
	// @DefaultErrorsResponse()
	// @Post(Api.evaluateFlag.url)
	// async evaluateFlag(@Req() req: Request): Promise<ResponsePayloadType<FeatureFlag[]>> {
	// 	const author = req.passportUser;
	// 	if (!author) {
	// 		return missingCredentialsResponse(req, Api.postFlagProfile);
	// 	}
	// 	const flags = await this.flagService.findFlags(author, {});
	// 	return successResponse(req, Api.evaluateFlag, flags);
	// }
}
