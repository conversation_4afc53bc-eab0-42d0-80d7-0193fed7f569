import { Injectable } from '@nestjs/common';
import NodeCache from 'node-cache';
import validator from 'validator';
import { RepositoryWrapper } from '../db/repository-wrapper';
import { User } from '../entity/user.entity';
import * as log from '../utils/log';

@Injectable()
export class UserService extends RepositoryWrapper<User> {
	static nodeCache = new NodeCache({ stdTTL: 90, checkperiod: 60 });

	constructor() {
		super(User);
	}

	/**
	 * Finds the Users table Column name, based on the format of the data provided
	 * Number -> 'id'
	 * Has @ symbol -> 'email'
	 * Is in uuid format -> 'uuid'
	 * @param userIdentity
	 * @returns column name
	 */
	getColumnNameByUserIdentity(userIdentity: string): string {
		if (validator.isNumeric(userIdentity)) {
			return 'id';
		}
		// isUUID returns false for many "valid" UUIDs generated by MySQL, so also using hyphen to detect uuid format
		return validator.isEmail(userIdentity) ? 'email' : validator.isUUID(userIdentity) || userIdentity.indexOf('-') > 0 ? 'uuid' : 'id';
	}

	/**
	 * Find UserProfile of Logged-in user (aka PassportUser)
	 * @calledBy [AIS] - Find Passport Author's Details
	 * @param userIdentity
	 * @param botId
	 * @returns Logged-in user's profile
	 */
	async findAuthor(userIdentity: string): Promise<User | null> {
		const NCK_FIND_AUTHOR = `pps-nck-author-${userIdentity}`;
		if (UserService.nodeCache.has(NCK_FIND_AUTHOR)) {
			return UserService.nodeCache.get(NCK_FIND_AUTHOR) as User;
		}
		const key = this.getColumnNameByUserIdentity(userIdentity);
		log.enter('UserService:findAuthor', `userIdentity=${userIdentity}`);
		const user = await this.dataSource
			.getRepository(User)
			.createQueryBuilder('u')
			.select(['u.id', 'u.orgId', 'u.firstName', 'u.lastName', 'u.email', 'u.uuid', 'u.active', 'u.managerUserId', 'u.settings', 'u.policy', 'u.createdAt', 'u.updatedAt'])
			.innerJoin('u.org', 'o')
			.addSelect(['o.id', 'o.name', 'o.type', 'o.ssoConfigId', 'o.settings', 'o.policy', 'o.active'])
			.leftJoin('u.roles', 'r')
			.addSelect(['r.id', 'r.name', 'r.description', 'r.tags', 'r.active'])
			.leftJoin('r.userRoles', 'ur')
			.addSelect(['ur.id', 'ur.userId', 'ur.uuid', 'ur.roleId', 'ur.orgId', 'ur.orgDivisionId', 'ur.active', 'ur.botId'])
			.leftJoin('r.policies', 'p')
			.addSelect(['p.id', 'p.name', 'p.description', 'p.version', 'p.policy'])
			.leftJoin('ur.org', 'urOrg')
			.addSelect(['urOrg.id', 'urOrg.name', 'urOrg.policy'])
			.where(`u.${key} = :userIdentity`, { userIdentity })
			.andWhere(`o.active = :active`, { active: 1 })
			.andWhere('r.id = ur.role_id')
			.andWhere('u.id = ur.user_id')
			.andWhere('u.active = :active', { active: 1 })
			.andWhere('ur.active = :active', { active: 1 })
			.getOne();

		const logKey = user ? `userEmail=${user.email}, uuid=${user.uuid} org=${user.org?.name}` : `${userIdentity} - not enabled for Passport`;
		log.exit('UserService:findAuthor', logKey);
		if (user) {
			UserService.nodeCache.set(NCK_FIND_AUTHOR, user);
		}
		return user;
	}
}
