CREATE TABLE `passport`.`feature_flags` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `description` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `type` enum('boolean','string','json','multivariate') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'boolean' COMMENT 'Type of Value',
  `stage` enum('Alpha','Beta','GA','Obsolete') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'Alpha' COMMENT 'Feature Status',
  `status` enum('In Use','Ready to Archive','Archived','Deprecated','Obsolete') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'In Use' COMMENT 'Archived Status',
  `version` int DEFAULT '1',
  `tags` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `settings` json DEFAULT NULL,
  `active` tinyint unsigned DEFAULT '1',
  `default_variant` tinyint DEFAULT NULL,
  `off_variant` tinyint DEFAULT NULL,
  `comments` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `history_notes` json DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int NOT NULL DEFAULT '1',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` int NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_feature_flags_name` (`name`),
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `passport`.`ff_variants` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `flag_id` int unsigned DEFAULT NULL,
  `var_index` tinyint DEFAULT NULL,
  `name` varchar(64) DEFAULT NULL,
  `value` text,
  `active` tinyint unsigned DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int unsigned NOT NULL DEFAULT '1',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` int unsigned NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_flag_id_var_index` (`flag_id`,`var_index`),
  KEY `ix_flag_id` (`flag_id`),
  KEY `ix_var_index` (`var_index`),
  CONSTRAINT `fk_ffv_flag_id` FOREIGN KEY (`flag_id`) REFERENCES `feature_flags` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=latin1;

CREATE TABLE `passport`.`ff_roles` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `flag_id` int unsigned NOT NULL,
  `org_id` int unsigned DEFAULT NULL,
  `org_division_id` int unsigned DEFAULT NULL,
  `user_id` int unsigned DEFAULT NULL,
  `bot_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `bot_ref_id` varchar(64) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  `service_name` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `env` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `version` int DEFAULT NULL,
  `on_variant` tinyint DEFAULT NULL,
  `negate` tinyint(1) DEFAULT '0',
  `settings` json DEFAULT NULL,
  `status` enum('Ready to Archive','Archived','Obsolete') COLLATE utf8mb4_general_ci DEFAULT NULL,
  `active` tinyint unsigned DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int unsigned NOT NULL DEFAULT '1',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` int unsigned NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `flag_id` (`flag_id`,`org_id`,`org_division_id`,`bot_id`,`bot_ref_id`,`env`),
  KEY `fk_orgs_id` (`org_id`),
  KEY `fk_org_divisions_id` (`org_division_id`),
  KEY `fk_bot_id` (`bot_id`),
  KEY `fk_user_id` (`user_id`),
  KEY `fk_bot_ref_id` (`bot_ref_id`),
  KEY `fk_on_variant` (`on_variant`),
  CONSTRAINT `fk_bot_id` FOREIGN KEY (`bot_id`) REFERENCES `studio`.`bots` (`uuid`),
  CONSTRAINT `fk_bot_ref_id` FOREIGN KEY (`bot_ref_id`) REFERENCES `studio`.`bot_social_config` (`bot_ref_id`),
  CONSTRAINT `fk_feature_flag_id` FOREIGN KEY (`flag_id`) REFERENCES `feature_flags` (`id`),
  CONSTRAINT `fk_on_variant` FOREIGN KEY (`on_variant`) REFERENCES `ff_variants` (`var_index`),
  CONSTRAINT `fk_org_divisions_id` FOREIGN KEY (`org_division_id`) REFERENCES `org_divisions` (`id`),
  CONSTRAINT `fk_orgs_id` FOREIGN KEY (`org_id`) REFERENCES `orgs` (`id`),
  CONSTRAINT `fk_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `passport`.`ff_connections` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `client_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `client_ip` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `client_host` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `flags` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status` enum('connect','subscribe-flags','disconnect','polling','unreachable') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `callbackType` enum('Rest','WebSocket','Webhook') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'WebSocket',
  `settings` json DEFAULT NULL,
  `notes` json DEFAULT NULL,
  `active` tinyint unsigned NOT NULL DEFAULT '1',
  `issued_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `disconnected_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_client_id` (`client_id`),
  KEY `ix_active` (`active`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

ALTER TABLE `passport`.`feature_flags` ADD CONSTRAINT `fk_default_variant` FOREIGN KEY (`default_variant`) REFERENCES `ff_variants` (`var_index`);
ALTER TABLE `passport`.`feature_flags` ADD CONSTRAINT `fk_off_variant` FOREIGN KEY (`off_variant`) REFERENCES `ff_variants` (`var_index`);
