{
	"extends": ["react-app", "prettier"],
	"plugins": ["react", "prettier"],
	"rules": {
		"linebreak-style": 0,
		// "indent": [2, { "SwitchCase": 1 }],
		"no-plusplus": ["error", { "allowForLoopAfterthoughts": true }],
		"import/no-extraneous-dependencies": ["error", { "devDependencies": true }],
		"no-restricted-syntax": ["error", "WithStatement", "BinaryExpression[operator='in']"],
		"no-unused-expressions": ["error", { "allowShortCircuit": true, "allowTernary": true }],
		"no-underscore-dangle": ["error", { "allowAfterThis": true }],
		"no-param-reassign": ["error", { "props": false }],
		"prefer-destructuring": ["error", { "object": false, "array": false }],
		"class-methods-use-this": "off",
		"no-console": 0,
		"no-constructor-return": "off",
		"arrow-body-style": "off",
		"no-new": "off",
		"max-len": ["error", { "code": 200 }],
		"guard-for-in": "off",
		"object-property-newline": "off",
		"object-curly-newline": "off"
		// "prettier/prettier": ["error", { "endOfLine": "auto", "tabWidth": 4 }]
	},
	"parserOptions": {
		"ecmaVersion": "latest",
		"sourceType": "module"
	},
	"ignorePatterns": ["src/migration/*.ts"]
}
