{"name": "feature-flag-service", "version": "0.2.4", "description": "Feature Flag Management Service", "author": "<PERSON><PERSON><PERSON>", "private": true, "license": "Netomi Inc.", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "dev": "tsc && node dist/index.js", "setup-env": "ts-node setup-env.ts", "cp-env": "cp .env ./dist/", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/index", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@msgai/passport-authz-js": "^0.3.2", "@nestjs/common": "^11.0.1", "@nestjs/core": "^11.0.1", "@nestjs/event-emitter": "^3.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/microservices": "^11.0.6", "@nestjs/platform-express": "^11.0.1", "@nestjs/platform-socket.io": "^11.0.6", "@nestjs/swagger": "^11.0.3", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.0.5", "axios": "^1.7.9", "compression": "^1.7.5", "csvtojson": "^2.0.10", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "express-session": "^1.18.1", "jsonexport": "^3.2.0", "jsonwebtoken": "^9.0.2", "kafkajs": "^2.2.4", "lodash": "^4.17.21", "minimatch": "^10.0.3", "multer": "^1.4.5-lts.1", "mysql2": "^3.12.0", "node-cache": "^5.1.2", "pako": "^2.1.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "typeorm": "^0.3.20", "validator": "^13.12.0"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/compression": "^1.7.5", "@types/dotenv": "^8.2.3", "@types/express": "^5.0.0", "@types/express-session": "^1.18.1", "@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.14", "@types/jsonexport": "^3.0.5", "@types/lodash": "^4.17.15", "@types/minimatch": "^6.0.0", "@types/node": "^22.10.7", "@types/pako": "^2.0.3", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "@types/validator": "^13.12.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^15.14.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}