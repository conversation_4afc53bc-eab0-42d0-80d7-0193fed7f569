import axios from 'axios';
import dotenv from 'dotenv';
import fs from 'fs';
import * as log from './src/utils/log';

dotenv.config();

const start = async () => {
	try {
		log.info('APP_ENV', process.env.APP_ENV);
		log.info('CONFIG_HOST', process.env.CONFIG_HOST);
		log.info('CONFIG_REGION', process.env.CONFIG_REGION);
		log.info('CONFIG_ENV', process.env.CONFIG_ENV);
		log.info('CONFIG_SERVICE_NAME', process.env.CONFIG_SERVICE_NAME);
		const res = await axios.post<{ payload: { configMap: { [key: string]: string } } }>(
			process.env.CONFIG_HOST!,
			{
				env: process.env.CONFIG_ENV,
				region: process.env.CONFIG_REGION,
				serviceName: process.env.CONFIG_SERVICE_NAME,
			},
			{ headers: { ContentType: 'application/json' } },
		);
		const config = res.data.payload.configMap;

		// log.info('ConfigManager values:', config);
		if (fs.existsSync('.env')) {
			log.debug('resetting existing .env file');
			fs.truncateSync('.env');
		}

		fs.writeFileSync(`config.json`, JSON.stringify(config, undefined, 2));
		Object.keys(config).forEach((key: string) => {
			const value = config[key];
			// const prefix = key?.startsWith('NEXT_PUBLIC_') ? '' : 'NEXT_PUBLIC_';
			fs.appendFileSync('.env', `${key}=${value}\n`);
		});
		log.info('Successfully added env variables to process.env');
	} catch (error) {
		log.error('Failed to complete setup-env', error);
	}
};

const p = start();
Promise.all([p]);
module.exports = {};
