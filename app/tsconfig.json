{"compilerOptions": {"target": "es2021", "module": "commonjs", "lib": ["dom", "es6", "es2021", "esnext.asynciterable"], "skipLibCheck": true, "sourceMap": true, "outDir": "./dist", "moduleResolution": "node", "removeComments": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "resolveJsonModule": true, "types": ["node", "jest"]}, "include": ["src/**/*.ts", "./cached-values", "./express.d.ts", "test/**/*.ts"], "exclude": ["./node_modules", "./dist"]}